﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\FY.WB.CSHero2.Application\FY.WB.CSHero2.Application.csproj" />
    <ProjectReference Include="..\FY.WB.CSHero2.Domain\FY.WB.CSHero2.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Storage.Blobs" Version="12.24.0" />
    <PackageReference Include="Finbuckle.MultiTenant.EntityFrameworkCore" Version="9.1.3" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.15" />
    <PackageReference Include="Microsoft.Azure.Cosmos" Version="3.51.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.15" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.15" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Migrations\" />
    <Folder Include="Persistence\Migrations\" />
    <Folder Include="Persistence\Seeders\" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Persistence\SeedData\clients.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Persistence\SeedData\forms.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Persistence\SeedData\invoices.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Persistence\SeedData\reports.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Persistence\SeedData\templates.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Persistence\SeedData\tenant-profiles.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Persistence\SeedData\uploads.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

</Project>
