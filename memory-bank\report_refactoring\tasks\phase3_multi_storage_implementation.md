# Phase 3: Multi-Storage Architecture Implementation

## Overview

Phase 3 implements the original multi-storage vision while preserving the SQL entities created in Phases 1-2. This phase transforms the current SQL-only implementation into a hybrid multi-storage architecture that leverages:

1. **SQL Database**: Report metadata and relationships (existing + extended)
2. **Azure Cosmos DB**: Dynamic report content and sections (new)
3. **Azure Blob Storage**: Rendered components and assets (new)

## Current State Assessment

### ✅ **Phase 1: COMPLETED**
- Domain entities created: `ReportSection` and `ReportSectionField`
- Entity configurations with proper relationships and indexes
- Navigation properties established between Report → ReportSection → ReportSectionField

### ✅ **Phase 2: COMPLETED**  
- Database migration `AddReportSectionEntities` applied
- Seed data created for sections and fields
- ApplicationDbContext and configurations updated

### ⚠️ **CRITICAL GAP IDENTIFIED**
Current implementation uses SQL-only storage but original architecture specifies multi-storage approach.

## Phase 3 Architecture

```mermaid
graph TB
    subgraph "SQL Database"
        A[Report Entity] --> B[ReportSection Entity]
        B --> C[ReportSectionField Entity]
        A --> D[ReportStyle Entity]
        A --> E[ReportVersion Entity]
    end
    
    subgraph "Azure Cosmos DB"
        F[ReportData Document]
        G[Section Data]
        H[Field Data]
        F --> G
        G --> H
    end
    
    subgraph "Azure Blob Storage"
        I[Component Files]
        J[Asset Files]
        K[Metadata JSON]
    end
    
    subgraph "Orchestration Layer"
        L[ReportService]
        M[ReportDataService]
        N[ReportRenderingService]
    end
    
    A -.->|DataDocumentId| F
    A -.->|ComponentsBlobId| I
    E -.->|DataDocumentId| F
    E -.->|ComponentsBlobId| I
    
    L --> A
    M --> F
    N --> I
    
    L --> M
    L --> N
```

## Detailed Tasks

### Task 3.1: Extend SQL Schema for Multi-Storage References

**Duration**: 1 day  
**Dependencies**: Phases 1-2 complete

**Steps:**

1. **Update Report Entity**
   ```csharp
   // Add to FY.WB.CSHero2.Domain/Entities/Report.cs
   public string? DataDocumentId { get; set; }  // Cosmos DB reference
   public string? ComponentsBlobId { get; set; } // Blob Storage reference
   ```

2. **Update ReportVersion Entity**
   ```csharp
   // Add to existing ReportVersion entity  
   public string? DataDocumentId { get; set; }
   public string? ComponentsBlobId { get; set; }
   public long DataSize { get; set; }
   public long ComponentsSize { get; set; }
   ```

3. **Create ReportStyle Entity**
   ```csharp
   // Create FY.WB.CSHero2.Domain/Entities/ReportStyle.cs
   public class ReportStyle : AuditedEntity<Guid>
   {
       public Guid ReportId { get; set; }
       public string Theme { get; set; } = "default";
       public string ColorScheme { get; set; } = "default";
       public string Typography { get; set; } = "default";
       public string Spacing { get; set; } = "default";
       
       // JSON serialized style options
       public string? LayoutOptionsJson { get; set; }
       public string? TypographyOptionsJson { get; set; }
       public string? StructureOptionsJson { get; set; }
       public string? ContentOptionsJson { get; set; }
       public string? VisualOptionsJson { get; set; }
       
       public virtual Report Report { get; set; } = null!;
   }
   ```

4. **Create ReportStyleConfiguration**
   ```csharp
   // Create FY.WB.CSHero2.Infrastructure/Persistence/Configurations/ReportStyleConfiguration.cs
   public class ReportStyleConfiguration : IEntityTypeConfiguration<ReportStyle>
   {
       public void Configure(EntityTypeBuilder<ReportStyle> builder)
       {
           builder.ToTable("ReportStyles");
           builder.HasKey(s => s.Id);
           
           builder.Property(s => s.Theme).HasMaxLength(50);
           builder.Property(s => s.ColorScheme).HasMaxLength(50);
           builder.Property(s => s.Typography).HasMaxLength(50);
           builder.Property(s => s.Spacing).HasMaxLength(50);
           
           builder.Property(s => s.LayoutOptionsJson).HasColumnType("nvarchar(max)");
           builder.Property(s => s.TypographyOptionsJson).HasColumnType("nvarchar(max)");
           builder.Property(s => s.StructureOptionsJson).HasColumnType("nvarchar(max)");
           builder.Property(s => s.ContentOptionsJson).HasColumnType("nvarchar(max)");
           builder.Property(s => s.VisualOptionsJson).HasColumnType("nvarchar(max)");
           
           builder.HasOne(s => s.Report)
               .WithOne()
               .HasForeignKey<ReportStyle>(s => s.ReportId)
               .OnDelete(DeleteBehavior.Cascade);
               
           builder.HasIndex(s => s.ReportId)
               .IsUnique()
               .HasDatabaseName("IX_ReportStyles_ReportId");
       }
   }
   ```

5. **Create and Apply Migration**
   ```bash
   dotnet ef migrations add AddMultiStorageReferences --project FY.WB.CSHero2.Infrastructure --startup-project FY.WB.CSHero2
   dotnet ef database update --project FY.WB.CSHero2.Infrastructure --startup-project FY.WB.CSHero2
   ```

**Acceptance Criteria:**
- SQL schema extended with storage reference fields
- ReportStyle entity created and configured
- Migration applied successfully
- No data loss during schema update

### Task 3.2: Implement Azure Infrastructure Setup

**Duration**: 1 day  
**Dependencies**: None

**Steps:**

1. **Azure Cosmos DB Setup**
   - Create Cosmos DB account with SQL API
   - Create database: "ReportData"
   - Create container: "Reports" with partition key "/id"
   - Configure throughput: 400 RU/s (autoscale)
   - Set up indexing policy for efficient queries

2. **Azure Blob Storage Setup**
   - Create Storage Account
   - Create container: "report-components"
   - Configure access tier: Hot
   - Set up CORS policy for web access
   - Configure lifecycle management for version retention

3. **Configuration Updates**
   ```json
   // Add to appsettings.json
   {
     "CosmosDb": {
       "ConnectionString": "AccountEndpoint=https://...",
       "DatabaseName": "ReportData",
       "ContainerName": "Reports"
     },
     "BlobStorage": {
       "ConnectionString": "DefaultEndpointsProtocol=https;...",
       "ContainerName": "report-components"
     }
   }
   ```

4. **Key Vault Integration**
   - Store connection strings in Azure Key Vault
   - Configure application to use Key Vault references
   - Update configuration for secure access

**Acceptance Criteria:**
- Azure Cosmos DB account and container created
- Azure Blob Storage account and container created
- Connection strings configured securely
- Infrastructure ready for application integration

### Task 3.3: Implement Cosmos DB Models and Repository

**Duration**: 2 days  
**Dependencies**: Task 3.2

**Steps:**

1. **Create Cosmos DB Models**
   ```csharp
   // Create FY.WB.CSHero2.Domain/Models/CosmosDb/ReportData.cs
   public class ReportData
   {
       [JsonPropertyName("id")]
       public string Id { get; set; } = string.Empty;
       
       [JsonPropertyName("reportId")]
       public string ReportId { get; set; } = string.Empty;
       
       [JsonPropertyName("versionId")]
       public string VersionId { get; set; } = string.Empty;
       
       [JsonPropertyName("versionNumber")]
       public int VersionNumber { get; set; }
       
       [JsonPropertyName("sections")]
       public List<CosmosReportSection> Sections { get; set; } = new();
       
       [JsonPropertyName("metadata")]
       public ReportDataMetadata Metadata { get; set; } = new();
   }
   
   public class CosmosReportSection
   {
       [JsonPropertyName("id")]
       public string Id { get; set; } = string.Empty;
       
       [JsonPropertyName("title")]
       public string Title { get; set; } = string.Empty;
       
       [JsonPropertyName("type")]
       public string Type { get; set; } = string.Empty;
       
       [JsonPropertyName("order")]
       public int Order { get; set; }
       
       [JsonPropertyName("fields")]
       public List<CosmosReportSectionField> Fields { get; set; } = new();
       
       [JsonPropertyName("metadata")]
       public Dictionary<string, object> Metadata { get; set; } = new();
   }
   
   public class CosmosReportSectionField
   {
       [JsonPropertyName("id")]
       public string Id { get; set; } = string.Empty;
       
       [JsonPropertyName("name")]
       public string Name { get; set; } = string.Empty;
       
       [JsonPropertyName("type")]
       public string Type { get; set; } = string.Empty;
       
       [JsonPropertyName("content")]
       public string Content { get; set; } = string.Empty;
       
       [JsonPropertyName("order")]
       public int Order { get; set; }
       
       [JsonPropertyName("metadata")]
       public Dictionary<string, object> Metadata { get; set; } = new();
   }
   
   public class ReportDataMetadata
   {
       [JsonPropertyName("createdAt")]
       public DateTime CreatedAt { get; set; }
       
       [JsonPropertyName("createdBy")]
       public string CreatedBy { get; set; } = string.Empty;
       
       [JsonPropertyName("lastModifiedAt")]
       public DateTime LastModifiedAt { get; set; }
       
       [JsonPropertyName("lastModifiedBy")]
       public string LastModifiedBy { get; set; } = string.Empty;
   }
   ```

2. **Create Repository Interface**
   ```csharp
   // Create FY.WB.CSHero2.Application/Common/Interfaces/IReportDataRepository.cs
   public interface IReportDataRepository
   {
       Task<ReportData?> GetReportDataAsync(string documentId, CancellationToken cancellationToken = default);
       Task<ReportData?> GetReportDataByReportIdAsync(string reportId, string versionId, CancellationToken cancellationToken = default);
       Task<string> CreateReportDataAsync(ReportData data, CancellationToken cancellationToken = default);
       Task UpdateReportDataAsync(ReportData data, CancellationToken cancellationToken = default);
       Task DeleteReportDataAsync(string documentId, CancellationToken cancellationToken = default);
       
       Task<CosmosReportSection?> GetSectionAsync(string documentId, string sectionId, CancellationToken cancellationToken = default);
       Task AddSectionAsync(string documentId, CosmosReportSection section, CancellationToken cancellationToken = default);
       Task UpdateSectionAsync(string documentId, CosmosReportSection section, CancellationToken cancellationToken = default);
       Task DeleteSectionAsync(string documentId, string sectionId, CancellationToken cancellationToken = default);
       
       Task<CosmosReportSectionField?> GetFieldAsync(string documentId, string sectionId, string fieldId, CancellationToken cancellationToken = default);
       Task AddFieldAsync(string documentId, string sectionId, CosmosReportSectionField field, CancellationToken cancellationToken = default);
       Task UpdateFieldAsync(string documentId, string sectionId, CosmosReportSectionField field, CancellationToken cancellationToken = default);
       Task DeleteFieldAsync(string documentId, string sectionId, string fieldId, CancellationToken cancellationToken = default);
   }
   ```

3. **Implement Repository**
   ```csharp
   // Create FY.WB.CSHero2.Infrastructure/Persistence/Repositories/ReportDataRepository.cs
   public class ReportDataRepository : IReportDataRepository
   {
       private readonly CosmosClient _cosmosClient;
       private readonly Container _container;
       private readonly ILogger<ReportDataRepository> _logger;
       
       public ReportDataRepository(
           CosmosClient cosmosClient, 
           IConfiguration configuration,
           ILogger<ReportDataRepository> logger)
       {
           _cosmosClient = cosmosClient;
           _logger = logger;
           
           var databaseName = configuration["CosmosDb:DatabaseName"];
           var containerName = configuration["CosmosDb:ContainerName"];
           _container = _cosmosClient.GetContainer(databaseName, containerName);
       }
       
       public async Task<ReportData?> GetReportDataAsync(string documentId, CancellationToken cancellationToken = default)
       {
           try
           {
               var response = await _container.ReadItemAsync<ReportData>(
                   documentId, 
                   new PartitionKey(documentId),
                   cancellationToken: cancellationToken);
                   
               return response.Resource;
           }
           catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
           {
               _logger.LogWarning("Report data document {DocumentId} not found", documentId);
               return null;
           }
           catch (Exception ex)
           {
               _logger.LogError(ex, "Error retrieving report data document {DocumentId}", documentId);
               throw;
           }
       }
       
       public async Task<string> CreateReportDataAsync(ReportData data, CancellationToken cancellationToken = default)
       {
           try
           {
               var response = await _container.CreateItemAsync(
                   data,
                   new PartitionKey(data.Id),
                   cancellationToken: cancellationToken);
                   
               _logger.LogInformation("Created report data document {DocumentId}", data.Id);
               return data.Id;
           }
           catch (Exception ex)
           {
               _logger.LogError(ex, "Error creating report data document {DocumentId}", data.Id);
               throw;
           }
       }
       
       // Additional methods implementation...
   }
   ```

**Acceptance Criteria:**
- Cosmos DB models created with proper JSON serialization
- Repository interface defined with all required operations
- Repository implementation handles CRUD operations
- Error handling and logging implemented
- Unit tests created for repository methods

### Task 3.4: Implement Blob Storage Models and Repository

**Duration**: 2 days  
**Dependencies**: Task 3.2

**Steps:**

1. **Create Blob Storage Models**
   ```csharp
   // Create FY.WB.CSHero2.Domain/Models/BlobStorage/ComponentsMetadata.cs
   public class ComponentsMetadata
   {
       [JsonPropertyName("reportId")]
       public string ReportId { get; set; } = string.Empty;
       
       [JsonPropertyName("versionId")]
       public string VersionId { get; set; } = string.Empty;
       
       [JsonPropertyName("versionNumber")]
       public int VersionNumber { get; set; }
       
       [JsonPropertyName("components")]
       public List<ComponentMetadata> Components { get; set; } = new();
       
       [JsonPropertyName("createdAt")]
       public DateTime CreatedAt { get; set; }
       
       [JsonPropertyName("createdBy")]
       public string CreatedBy { get; set; } = string.Empty;
   }
   
   public class ComponentMetadata
   {
       [JsonPropertyName("id")]
       public string Id { get; set; } = string.Empty;
       
       [JsonPropertyName("name")]
       public string Name { get; set; } = string.Empty;
       
       [JsonPropertyName("sectionId")]
       public string SectionId { get; set; } = string.Empty;
       
       [JsonPropertyName("fileName")]
       public string FileName { get; set; } = string.Empty;
       
       [JsonPropertyName("imports")]
       public List<string> Imports { get; set; } = new();
       
       [JsonPropertyName("props")]
       public List<string> Props { get; set; } = new();
   }
   
   public class ComponentDefinition
   {
       public string Id { get; set; } = string.Empty;
       public string Name { get; set; } = string.Empty;
       public string SectionId { get; set; } = string.Empty;
       public string Code { get; set; } = string.Empty;
       public List<string> Imports { get; set; } = new();
       public List<string> Props { get; set; } = new();
   }
   ```

2. **Create Repository Interface**
   ```csharp
   // Create FY.WB.CSHero2.Application/Common/Interfaces/IReportComponentsRepository.cs
   public interface IReportComponentsRepository
   {
       Task<string> SaveComponentsAsync(Guid reportId, Guid versionId, IEnumerable<ComponentDefinition> components, CancellationToken cancellationToken = default);
       Task<ComponentsMetadata?> GetComponentsMetadataAsync(string blobId, CancellationToken cancellationToken = default);
       Task<ComponentDefinition?> GetComponentAsync(string blobId, string componentName, CancellationToken cancellationToken = default);
       Task<IEnumerable<ComponentDefinition>> GetAllComponentsAsync(string blobId, CancellationToken cancellationToken = default);
       Task DeleteComponentsAsync(string blobId, CancellationToken cancellationToken = default);
       Task<byte[]> ExportComponentsAsZipAsync(string blobId, CancellationToken cancellationToken = default);
   }
   ```

3. **Implement Repository**
   ```csharp
   // Create FY.WB.CSHero2.Infrastructure/Persistence/Repositories/ReportComponentsRepository.cs
   public class ReportComponentsRepository : IReportComponentsRepository
   {
       private readonly BlobServiceClient _blobServiceClient;
       private readonly string _containerName;
       private readonly ILogger<ReportComponentsRepository> _logger;
       
       public ReportComponentsRepository(
           BlobServiceClient blobServiceClient, 
           IConfiguration configuration,
           ILogger<ReportComponentsRepository> logger)
       {
           _blobServiceClient = blobServiceClient;
           _logger = logger;
           _containerName = configuration["BlobStorage:ContainerName"];
       }
       
       public async Task<string> SaveComponentsAsync(
           Guid reportId, 
           Guid versionId, 
           IEnumerable<ComponentDefinition> components, 
           CancellationToken cancellationToken = default)
       {
           try
           {
               var blobId = $"reports/{reportId}/{versionId}";
               var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
               
               // Ensure container exists
               await containerClient.CreateIfNotExistsAsync(cancellationToken: cancellationToken);
               
               // Create metadata
               var metadata = new ComponentsMetadata
               {
                   ReportId = reportId.ToString(),
                   VersionId = versionId.ToString(),
                   CreatedAt = DateTime.UtcNow,
                   CreatedBy = "system", // This should be replaced with the actual user ID
                   Components = components.Select(c => new ComponentMetadata
                   {
                       Id = c.Id,
                       Name = c.Name,
                       SectionId = c.SectionId,
                       FileName = $"{c.Name}.tsx",
                       Imports = c.Imports,
                       Props = c.Props
                   }).ToList()
               };
               
               // Save metadata
               var metadataJson = JsonSerializer.Serialize(metadata, new JsonSerializerOptions
               {
                   WriteIndented = true,
                   PropertyNamingPolicy = JsonNamingPolicy.CamelCase
               });
               
               var metadataBlobClient = containerClient.GetBlobClient($"{blobId}/metadata.json");
               using var metadataStream = new MemoryStream(Encoding.UTF8.GetBytes(metadataJson));
               await metadataBlobClient.UploadAsync(metadataStream, overwrite: true, cancellationToken: cancellationToken);
               
               // Save components
               foreach (var component in components)
               {
                   var componentBlobClient = containerClient.GetBlobClient($"{blobId}/components/{component.Name}.tsx");
                   using var componentStream = new MemoryStream(Encoding.UTF8.GetBytes(component.Code));
                   await componentBlobClient.UploadAsync(componentStream, overwrite: true, cancellationToken: cancellationToken);
               }
               
               _logger.LogInformation("Saved {ComponentCount} components for report {ReportId}, version {VersionId}", 
                   components.Count(), reportId, versionId);
                   
               return blobId;
           }
           catch (Exception ex)
           {
               _logger.LogError(ex, "Error saving components for report {ReportId}, version {VersionId}", 
                   reportId, versionId);
               throw;
           }
       }
       
       // Additional methods implementation...
   }
   ```

**Acceptance Criteria:**
- Blob storage models created with proper JSON serialization
- Repository interface defined with all required operations
- Repository implementation handles blob operations
- Component files stored with proper structure
- Metadata tracking implemented
- Unit tests created for repository methods

### Task 3.5: Implement Orchestration Services

**Duration**: 3 days  
**Dependencies**: Tasks 3.3, 3.4

**Steps:**

1. **Create Service Interfaces**
   ```csharp
   // Create FY.WB.CSHero2.Application/Common/Interfaces/IReportService.cs
   public interface IReportService
   {
       Task<ReportDto> GetReportAsync(Guid reportId, CancellationToken cancellationToken = default);
       Task<IEnumerable<ReportDto>> GetReportsAsync(Guid? tenantId = null, CancellationToken cancellationToken = default);
       Task<Guid> CreateReportAsync(CreateReportDto request, CancellationToken cancellationToken = default);
       Task UpdateReportAsync(UpdateReportDto request, CancellationToken cancellationToken = default);
       Task DeleteReportAsync(Guid reportId, CancellationToken cancellationToken = default);
       Task<ReportVersionDto> CreateVersionAsync(Guid reportId, string description, CancellationToken cancellationToken = default);
       Task<ReportStyleDto> GetReportStyleAsync(Guid reportId, CancellationToken cancellationToken = default);
       Task UpdateReportStyleAsync(Guid reportId, ReportStyleDto style, CancellationToken cancellationToken = default);
   }
   
   // Create FY.WB.CSHero2.Application/Common/Interfaces/IReportDataService.cs
   public interface IReportDataService
   {
       Task<ReportDataDto> GetReportDataAsync(Guid reportId, CancellationToken cancellationToken = default);
       Task UpdateReportDataAsync(Guid reportId, ReportDataDto data, CancellationToken cancellationToken = default);
       Task<ReportSectionDto> GetSectionAsync(Guid reportId, string sectionId, CancellationToken cancellationToken = default);
       Task<ReportSectionDto> AddSectionAsync(Guid reportId, CreateReportSectionDto section, CancellationToken cancellationToken = default);
       Task UpdateSectionAsync(Guid reportId, UpdateReportSectionDto section, CancellationToken cancellationToken = default);
       Task DeleteSectionAsync(Guid reportId, string sectionId, CancellationToken cancellationToken = default);
       Task<ReportSectionFieldDto> AddFieldAsync(Guid reportId, string sectionId, CreateReportSectionFieldDto field, CancellationToken cancellationToken = default);
       Task UpdateFieldAsync(Guid reportId, string sectionId, UpdateReportSectionFieldDto field, CancellationToken cancellationToken = default);
       Task DeleteFieldAsync(Guid reportId, string sectionId, string fieldId, CancellationToken cancellationToken = default);
   }
   
   // Create FY.WB.CSHero2.Application/Common/Interfaces/IReportRenderingService.cs
   public interface IReportRenderingService
   {
       Task<RenderResultDto> RenderReportAsync(Guid reportId, CancellationToken cancellationToken = default);
       Task<ComponentDefinitionDto> GetComponentAsync(Guid reportId, string componentId, CancellationToken cancellationToken = default);
       Task<IEnumerable<ComponentDefinitionDto>> GetComponentsAsync(Guid reportId, CancellationToken cancellationToken = default);
       Task<byte[]> ExportReportAsync(Guid reportId, ExportFormat format, CancellationToken cancellationToken = default);
   }
   ```

2. **Create DTOs**
   ```csharp
   // Create FY.WB.CSHero2.Application/Reports/DTOs/ReportDto.cs
   public class ReportDto
   {
       public Guid Id { get; set; }
       public string Name { get; set; } = string.Empty;
       public string Description { get; set; } = string.Empty;
       public Guid? TenantId { get; set; }
       public DateTime CreatedAt { get; set; }
       public DateTime? LastModifiedAt { get; set; }
       public bool IsPublished { get; set; }
       public bool IsArchived { get; set; }
       public string? DataDocumentId { get; set; }
       public string? ComponentsBlobId { get; set; }
       public ReportVersionDto? CurrentVersion { get; set; }
       public ReportStyleDto? Style { get; set; }
   }
   
   public class CreateReportDto
   {
       public string Name { get; set; } = string.Empty;
       public string Description { get; set; } = string.Empty;
       public Guid? TenantId { get; set; }
       public string? Theme { get; set; }
       public string? ColorScheme { get; set; }
       public string? Typography { get; set; }
       public string? Spacing { get; set; }
   }
   
   public class ReportDataDto
   {
       public string Id { get; set; } = string.Empty;
       public string ReportId { get; set; } = string.Empty;
       public string VersionId { get; set; } = string.Empty;
       public int VersionNumber { get; set; }
       public List<ReportSectionDto> Sections { get; set; } = new();
       public ReportDataMetadataDto Metadata { get; set; } = new();
   }
   
   public class ReportSectionDto
   {
       public string Id { get; set; } = string.Empty;
       public string Title { get; set; } = string.Empty;
       public string Type { get; set; } = string.Empty;
       public int Order { get; set; }
       public List<ReportSectionFieldDto> Fields { get; set; } = new();
   }
   ```

3. **Implement Services**
   ```csharp
   // Create FY.WB.CSHero2.Application/Reports/Services/ReportService.cs
   public class ReportService : IReportService
   {
       private readonly IApplicationDbContext _dbContext;
       private readonly IReportDataRepository _dataRepository;
       private readonly IReportComponentsRepository _componentsRepository;
       private readonly IMapper _mapper;
       private readonly ILogger<ReportService> _logger;
       
       public ReportService(
           IApplicationDbContext dbContext,
           IReportDataRepository dataRepository,
           IReportComponentsRepository componentsRepository,
           IMapper mapper,
           ILogger<ReportService> logger)
       {
           _dbContext = dbContext;
           _dataRepository = dataRepository;
           _componentsRepository = componentsRepository;
           _mapper = mapper;
           _logger = logger;
       }
       
       public async Task<ReportDto> GetReportAsync(Guid reportId, CancellationToken cancellationToken = default)
       {
           var report = await _dbContext.Reports
               .Include(r => r.Versions.Where(v => v.IsCurrent))
               .FirstOrDefaultAsync(r => r.Id == reportId, cancellationToken);
               
           if (report == null)
           {
               return null;
           }
           
           return _mapper.Map<ReportDto>(report);
       }
       
       public async Task<Guid> CreateReportAsync(CreateReportDto request, CancellationToken cancellationToken = default)
       {
           var report = _mapper.Map<Report>(request);
           report.Id = Guid.NewGuid();
           
           // Create initial version
           var version = new ReportVersion
           {
               Id = Guid.NewGuid(),
               ReportId = report.Id,
               VersionNumber = 1,
               VersionName = "Initial Version",
               IsCurrent = true,
               IsPublished = false
           };
           
           // Create initial style
           var style = new ReportStyle
           {
               Id = Guid.NewGuid(),
               ReportId = report.Id,
               Theme = request.Theme ?? "default",
               ColorScheme = request.ColorScheme ?? "default",
               Typography = request.Typography ?? "default",
               Spacing = request.Spacing ?? "default"
           };
           
           // Create initial report data in Cosmos DB
           var reportData = new ReportData
           {
               Id = $"report-data-{Guid.NewGuid()}",
               ReportId = report.Id.ToString(),
               VersionId = version.Id.ToString(),
               VersionNumber = version.VersionNumber,
               Sections = new List<CosmosReportSection>(),
               Metadata = new ReportDataMetadata
               {
                   CreatedAt = DateTime.UtcNow,
                   CreatedBy = "system", // Replace with actual user ID
                   LastModifiedAt = DateTime.UtcNow,
                   LastModifiedBy = "system"
               }
           };
           
           var documentId = await _dataRepository.CreateReportDataAsync(reportData, cancellationToken);
           
           // Update references
           report.DataDocumentId = documentId;
           version.DataDocumentId = documentId;
           
           // Save to SQL
           _dbContext.Reports.Add(report);
           _dbContext.ReportVersions.Add(version);
           _dbContext.ReportStyles.Add(style);
           
           await _dbContext.SaveChangesAsync(cancellationToken);
           
           return report.Id;
       }
       
       // Additional methods implementation...
   }
   ```

**Acceptance Criteria:**
- Service interfaces defined with comprehensive operations
- DTOs created for all data transfer scenarios
- Service implementations coordinate across storage types
- Proper error handling and logging
- Unit tests created for all service methods

### Task 3.6: Implement Data Migration Service

**Duration**: 2 days  
**Dependencies**: Task 3.5

**Steps:**

1. **Create Migration Service Interface**
   ```csh