using System;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Test
{
    public static class SeedTestData
    {
        private static readonly JsonSerializerOptions _jsonOptions = new()
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        };

        public static async Task Main(string[] args)
        {
            Console.WriteLine("Seeding test data for integration tests...");
            
            string baseUrl = "http://localhost:5056";
            var client = new HttpClient { BaseAddress = new Uri(baseUrl) };

            try
            {
                // First check if the API is running
                var healthResponse = await client.GetAsync("/api/health");
                if (!healthResponse.IsSuccessStatusCode)
                {
                    Console.WriteLine($"API is not running at {baseUrl}. Please start the API first.");
                    return;
                }

                // Login as admin and tenant users to get tokens
                var adminToken = await GetAuthToken(client, "<EMAIL>", "AdminPass123!");
                var tenant1Token = await GetAuthToken(client, "<EMAIL>", "Test123!");

                var adminClient = CreateAuthenticatedClient(client.BaseAddress, adminToken);
                var tenant1Client = CreateAuthenticatedClient(client.BaseAddress, tenant1Token);

                // Create test forms
                await CreateTestForm(
                    adminClient,
                    "Admin Test Form",
                    "Test Customer",
                    "<EMAIL>",
                    "test",
                    "high",
                    "This is a test form created by admin");

                await CreateTestForm(
                    tenant1Client,
                    "Tenant1 Test Form",
                    "Tenant Customer",
                    "<EMAIL>",
                    "test",
                    "medium",
                    "This is a test form created by tenant1");

                Console.WriteLine("Test data seeding completed!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error seeding test data: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
            }
            finally
            {
                client.Dispose();
            }
        }

        private static async Task<string> GetAuthToken(HttpClient client, string email, string password)
        {
            Console.WriteLine($"Getting auth token for {email}");

            var loginModel = new
            {
                Email = email,
                Password = password
            };

            var content = new StringContent(
                JsonSerializer.Serialize(loginModel, _jsonOptions),
                Encoding.UTF8,
                "application/json");

            var response = await client.PostAsync("/api/auth/login", content);
            
            if (response.IsSuccessStatusCode)
            {
                var responseJson = await response.Content.ReadFromJsonAsync<JsonElement>();
                var token = responseJson.GetProperty("token").GetString();
                Console.WriteLine($"Successfully obtained token for {email}");
                return token ?? throw new InvalidOperationException("Token was null in successful response");
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                throw new Exception($"Failed to get auth token for {email}: {response.StatusCode}, Error: {errorContent}");
            }
        }

        private static HttpClient CreateAuthenticatedClient(Uri baseAddress, string token)
        {
            var client = new HttpClient { BaseAddress = baseAddress };
            client.DefaultRequestHeaders.Add("Authorization", $"Bearer {token}");
            return client;
        }

        private static async Task CreateTestForm(
            HttpClient client,
            string title,
            string customerName,
            string email,
            string category,
            string priority,
            string description)
        {
            Console.WriteLine($"Creating test form: {title}");

            var form = new
            {
                Id = Guid.NewGuid().ToString(),
                Title = title,
                CustomerName = customerName,
                Email = email,
                Category = category,
                Priority = priority,
                Description = description,
                Date = DateTime.UtcNow.ToString("o")
            };

            var content = new StringContent(
                JsonSerializer.Serialize(form, _jsonOptions),
                Encoding.UTF8,
                "application/json");

            var response = await client.PostAsync("/api/forms", content);
            
            if (response.IsSuccessStatusCode)
            {
                Console.WriteLine($"Successfully created form: {title}");
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"Failed to create form {title}: {response.StatusCode}");
                Console.WriteLine($"Error: {errorContent}");
            }
        }
    }
}
