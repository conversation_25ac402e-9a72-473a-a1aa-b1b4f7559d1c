# Fix Foreign Key Issues in Seed Data
# This script fixes mismatches between related entities in JSON seed files

Write-Host "=== Seed Data Foreign Key Fix Tool ===" -ForegroundColor Cyan
Write-Host "Fixing relationships between seed data files..." -ForegroundColor Yellow

$seedDataPath = "FY.WB.CSHero2.Infrastructure\Persistence\SeedData"

# Create backup timestamp
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$backupPath = "seed-data-backup-$timestamp"
New-Item -ItemType Directory -Path $backupPath -Force | Out-Null

Write-Host "Creating backup in: $backupPath" -ForegroundColor Green

# Read all seed data files and create backups
try {
    $clients = Get-Content "$seedDataPath\clients.json" | ConvertFrom-Json
    $reports = Get-Content "$seedDataPath\reports.json" | ConvertFrom-Json
    $reportVersions = Get-Content "$seedDataPath\report-versions.json" | ConvertFrom-Json
    $reportSections = Get-Content "$seedDataPath\report-sections.json" | ConvertFrom-Json
    $reportSectionFields = Get-Content "$seedDataPath\report-section-fields.json" | ConvertFrom-Json
    
    # Create backups
    Copy-Item "$seedDataPath\reports.json" "$backupPath\reports.json.backup"
    Copy-Item "$seedDataPath\report-versions.json" "$backupPath\report-versions.json.backup"
    Copy-Item "$seedDataPath\report-sections.json" "$backupPath\report-sections.json.backup"
    Copy-Item "$seedDataPath\report-section-fields.json" "$backupPath\report-section-fields.json.backup"
    
    Write-Host "Backup completed successfully" -ForegroundColor Green
} catch {
    Write-Host "Error reading seed data files: $_" -ForegroundColor Red
    exit 1
}

Write-Host "`n1. FIXING CLIENT ID MISMATCHES IN REPORTS" -ForegroundColor Green
Write-Host "=" * 50

# Create lookup tables
$clientLookup = @{}
$clients | ForEach-Object { $clientLookup[$_.Id] = $_ }

$clientByCompanyAndTenant = @{}
$clients | ForEach-Object { 
    $key = "$($_.CompanyName)|$($_.TenantId)"
    $clientByCompanyAndTenant[$key] = $_
}

# Fix reports with invalid client references
$fixedReports = @()
$skippedReports = @()
$fixedCount = 0

foreach ($report in $reports) {
    $clientExists = $clientLookup.ContainsKey($report.clientId)
    
    if (-not $clientExists) {
        # Try to find correct client by company name and tenant
        $lookupKey = "$($report.clientName)|$($report.tenantId)"
        
        if ($clientByCompanyAndTenant.ContainsKey($lookupKey)) {
            $correctClient = $clientByCompanyAndTenant[$lookupKey]
            Write-Host "Fixing Report '$($report.name)': $($report.clientId) -> $($correctClient.Id)" -ForegroundColor Yellow
            
            # Update the report with correct client ID
            $report.clientId = $correctClient.Id
            $fixedReports += $report.id
            $fixedCount++
        } else {
            Write-Host "Cannot fix Report '$($report.name)': Client '$($report.clientName)' not found for tenant $($report.tenantId)" -ForegroundColor Red
            $skippedReports += $report.id
        }
    }
}

Write-Host "Fixed $fixedCount client ID mismatches" -ForegroundColor Green
Write-Host "Skipped $($skippedReports.Count) reports (client not found)" -ForegroundColor Yellow

# Save fixed reports.json
$reports | ConvertTo-Json -Depth 10 | Out-File "$seedDataPath\reports.json" -Encoding UTF8
Write-Host "Updated reports.json" -ForegroundColor Green

Write-Host "`n2. CLEANING UP ORPHANED REPORT VERSIONS" -ForegroundColor Green
Write-Host "=" * 50

# Create report lookup after fixes
$reportLookup = @{}
$reports | ForEach-Object { $reportLookup[$_.id] = $_ }

# Filter out orphaned report versions
$validVersions = @()
$removedVersions = @()

foreach ($version in $reportVersions) {
    if ($reportLookup.ContainsKey($version.reportId) -and $skippedReports -notcontains $version.reportId) {
        $validVersions += $version
    } else {
        $removedVersions += $version.id
        Write-Host "Removing orphaned version: $($version.id) (Report: $($version.reportId))" -ForegroundColor Yellow
    }
}

Write-Host "Kept $($validVersions.Count) valid report versions" -ForegroundColor Green
Write-Host "Removed $($removedVersions.Count) orphaned report versions" -ForegroundColor Yellow

# Save cleaned report-versions.json
$validVersions | ConvertTo-Json -Depth 10 | Out-File "$seedDataPath\report-versions.json" -Encoding UTF8
Write-Host "Updated report-versions.json" -ForegroundColor Green

Write-Host "`n3. CLEANING UP ORPHANED REPORT SECTIONS" -ForegroundColor Green
Write-Host "=" * 50

# Filter out orphaned report sections
$validSections = @()
$removedSections = @()

foreach ($section in $reportSections) {
    if ($reportLookup.ContainsKey($section.reportId) -and $skippedReports -notcontains $section.reportId) {
        $validSections += $section
    } else {
        $removedSections += $section.id
        Write-Host "Removing orphaned section: $($section.id) '$($section.title)' (Report: $($section.reportId))" -ForegroundColor Yellow
    }
}

Write-Host "Kept $($validSections.Count) valid report sections" -ForegroundColor Green
Write-Host "Removed $($removedSections.Count) orphaned report sections" -ForegroundColor Yellow

# Save cleaned report-sections.json
$validSections | ConvertTo-Json -Depth 10 | Out-File "$seedDataPath\report-sections.json" -Encoding UTF8
Write-Host "Updated report-sections.json" -ForegroundColor Green

Write-Host "`n4. CLEANING UP ORPHANED REPORT SECTION FIELDS" -ForegroundColor Green
Write-Host "=" * 50

# Create section lookup after cleanup
$sectionLookup = @{}
$validSections | ForEach-Object { $sectionLookup[$_.id] = $_ }

# Filter out orphaned report section fields
$validFields = @()
$removedFields = @()

foreach ($field in $reportSectionFields) {
    if ($sectionLookup.ContainsKey($field.sectionId)) {
        $validFields += $field
    } else {
        $removedFields += $field.id
        Write-Host "Removing orphaned field: $($field.id) '$($field.name)' (Section: $($field.sectionId))" -ForegroundColor Yellow
    }
}

Write-Host "Kept $($validFields.Count) valid report section fields" -ForegroundColor Green
Write-Host "Removed $($removedFields.Count) orphaned report section fields" -ForegroundColor Yellow

# Save cleaned report-section-fields.json
$validFields | ConvertTo-Json -Depth 10 | Out-File "$seedDataPath\report-section-fields.json" -Encoding UTF8
Write-Host "Updated report-section-fields.json" -ForegroundColor Green

Write-Host "`n5. SUMMARY" -ForegroundColor Green
Write-Host "=" * 50

Write-Host "FIXES APPLIED:" -ForegroundColor Yellow
Write-Host "- Fixed $fixedCount client ID mismatches in reports" -ForegroundColor White
Write-Host "- Removed $($removedVersions.Count) orphaned report versions" -ForegroundColor White
Write-Host "- Removed $($removedSections.Count) orphaned report sections" -ForegroundColor White
Write-Host "- Removed $($removedFields.Count) orphaned report section fields" -ForegroundColor White

if ($skippedReports.Count -gt 0) {
    Write-Host "`nWARNING: $($skippedReports.Count) reports still have issues (client not found):" -ForegroundColor Red
    $skippedReports | ForEach-Object { Write-Host "  - $_" -ForegroundColor Red }
}

Write-Host "`nBACKUP LOCATION: $backupPath" -ForegroundColor Cyan
Write-Host "All seed data files have been updated and should now pass FK validation" -ForegroundColor Green

# Generate summary report
$summary = @{
    Timestamp = $timestamp
    BackupPath = $backupPath
    FixedClientRefs = $fixedCount
    RemovedVersions = $removedVersions.Count
    RemovedSections = $removedSections.Count
    RemovedFields = $removedFields.Count
    SkippedReports = $skippedReports
    FixedReports = $fixedReports
}

$summary | ConvertTo-Json -Depth 10 | Out-File "seed-data-fix-summary.json" -Encoding UTF8
Write-Host "`nFix summary exported to: seed-data-fix-summary.json" -ForegroundColor Cyan
