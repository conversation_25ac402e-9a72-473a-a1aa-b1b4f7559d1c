using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Xunit.Abstractions;
using FY.WB.CSHero2.Infrastructure.Persistence;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Entities;
using FY.WB.CSHero2.ReportRenderingEngine.Infrastructure;
using FY.WB.CSHero2.Domain.Entities;
using Xunit;
using FluentAssertions;

namespace FY.WB.CSHero2.Test.ReportRenderingEngine
{
    /// <summary>
    /// Base class for ReportRenderingEngine integration tests with real data
    /// </summary>
    public abstract class ReportRenderingIntegrationTestBase : TestBase, IDisposable
    {
        protected readonly IServiceProvider ServiceProvider;
        protected readonly ApplicationDbContext DbContext;
        protected readonly ILogger Logger;
        protected readonly ReportTestDataSeeder DataSeeder;

        // Test data references
        protected Guid TestTenantId;
        protected Guid TestUserId;
        protected Template TestTemplate;
        protected Report TestReport;
        protected ReportVersion TestReportVersion;
        protected ComponentDefinition TestComponent;
        protected ReportStyleDocument TestStyleDocument;

        protected ReportRenderingIntegrationTestBase(ITestOutputHelper output)
        {
            // Setup real services with test configuration
            var services = new ServiceCollection();
            ConfigureTestServices(services);
            ServiceProvider = services.BuildServiceProvider();

            DbContext = ServiceProvider.GetRequiredService<ApplicationDbContext>();
            Logger = ServiceProvider.GetRequiredService<ILogger<ReportRenderingIntegrationTestBase>>();
            DataSeeder = new ReportTestDataSeeder();

            // Initialize test data
            InitializeTestDataAsync().Wait();
        }

        private void ConfigureTestServices(IServiceCollection services)
        {
            // Configuration
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.Test.json", optional: true)
                .AddEnvironmentVariables()
                .Build();

            services.AddSingleton<IConfiguration>(configuration);

            // Configure SQL Server with test database
            services.AddDbContext<ApplicationDbContext>(options =>
                options.UseSqlServer(GetTestConnectionString()));

            // Add logging
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.SetMinimumLevel(LogLevel.Debug);
            });

            // Add Report Rendering Engine Infrastructure with real storage implementations
            services.AddReportRenderingEngineInfrastructure(configuration);
        }

        private async Task InitializeTestDataAsync()
        {
            try
            {
                // Ensure database is created
                await DbContext.Database.EnsureCreatedAsync();

                // Initialize storage systems
                await ServiceProvider.EnsureStorageCreatedAsync();

                // Get services for seeding
                var styleService = ServiceProvider.GetRequiredService<IReportStyleService>();
                var blobService = ServiceProvider.GetRequiredService<IReportDataBlobService>();

                // Seed test data
                await DataSeeder.SeedTestDataAsync(DbContext, styleService, blobService);

                // Get references to seeded data
                TestTenantId = DataSeeder.TestTenantId;
                TestUserId = DataSeeder.TestUserId;
                TestTemplate = DataSeeder.TestTemplate;
                TestReport = DataSeeder.TestReport;
                TestReportVersion = DataSeeder.TestReportVersion;
                TestComponent = DataSeeder.TestComponent;
                TestStyleDocument = DataSeeder.TestStyleDocument;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to initialize test data: {ex.Message}", ex);
            }
        }

        private string GetTestConnectionString()
        {
            // Use a test-specific database. Correction. Using a dev database
            return "Server=(localdb)\\mssqllocaldb;Database=FY.WB.CSHero2;Trusted_Connection=true;MultipleActiveResultSets=true;";
        }

        /// <summary>
        /// Creates a new test report for testing
        /// </summary>
        protected async Task<Report> CreateTestReportAsync(string name = "Test Report")
        {
            var report = new Report(
                Guid.NewGuid(),
                $"TST-{DateTime.UtcNow:yyyyMMdd}-{Guid.NewGuid().ToString()[..8]}",
                TestTenantId,
                "Test Client",
                name,
                "Test Category",
                1,
                "Draft",
                "Test Author"
            );

            // TenantId and CreatorId will be set by DbContext

            DbContext.Reports.Add(report);
            await DbContext.SaveChangesAsync();

            return report;
        }

        /// <summary>
        /// Creates a new test report version for testing
        /// </summary>
        protected async Task<ReportVersion> CreateTestReportVersionAsync(Guid reportId, int versionNumber = 1)
        {
            var version = new ReportVersion
            {
                ReportId = reportId,
                VersionNumber = versionNumber,
                Description = $"Test version {versionNumber}",
                CreationTime = DateTime.UtcNow,
                CreatorId = TestUserId,
                IsCurrent = true,
                JsonData = """{"test": "data"}""",
                JsonDataSize = 16
            };

            DbContext.ReportVersions.Add(version);
            await DbContext.SaveChangesAsync();

            return version;
        }

        /// <summary>
        /// Creates a test style document
        /// </summary>
        protected ReportStyleDocument CreateTestStyleDocument(Guid? reportVersionId = null, Guid? templateId = null)
        {
            var styleDoc = new ReportStyleDocument
            {
                Id = reportVersionId.HasValue 
                    ? ReportStyleDocument.CreateReportStyleId(reportVersionId.Value)
                    : ReportStyleDocument.CreateTemplateStyleId(templateId ?? Guid.NewGuid()),
                PartitionKey = TestTenantId.ToString(),
                ReportVersionId = reportVersionId,
                TemplateId = templateId,
                TenantId = TestTenantId,
                StyleType = reportVersionId.HasValue ? "report" : "template",
                HtmlContent = "<div class='test-report'>Test Content</div>",
                CssStyles = ".test-report { color: blue; }",
                CreatedBy = TestUserId,
                LastModifiedBy = TestUserId
            };

            return styleDoc;
        }

        /// <summary>
        /// Creates test report data
        /// </summary>
        protected Dictionary<string, object> CreateTestReportData()
        {
            return new Dictionary<string, object>
            {
                ["header.title"] = "Test Report Title",
                ["header.date"] = DateTime.UtcNow.ToString("yyyy-MM-dd"),
                ["financials.revenue"] = 1000000,
                ["financials.expenses"] = 750000,
                ["charts.revenueChart"] = new
                {
                    type = "line",
                    data = new[] { 100, 200, 300, 400, 500 }
                }
            };
        }

        /// <summary>
        /// Asserts that a report version has the expected structure
        /// </summary>
        protected void AssertReportVersionStructure(ReportVersion version)
        {
            version.Should().NotBeNull();
            version.Id.Should().NotBeEmpty();
            version.ReportId.Should().NotBeEmpty();
            version.VersionNumber.Should().BeGreaterThan(0);
            version.CreationTime.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(5));
        }

        /// <summary>
        /// Asserts that a style document has the expected structure
        /// </summary>
        protected void AssertStyleDocumentStructure(ReportStyleDocument styleDoc)
        {
            styleDoc.Should().NotBeNull();
            styleDoc.Id.Should().NotBeNullOrEmpty();
            styleDoc.TenantId.Should().NotBeEmpty();
            styleDoc.StyleType.Should().BeOneOf("report", "template", "component");
            styleDoc.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(5));
        }

        /// <summary>
        /// Cleans up test data after each test
        /// </summary>
        protected async Task CleanupTestDataAsync()
        {
            try
            {
                // Remove test data from database
                var reports = await DbContext.Reports
                    .Where(r => r.TenantId == TestTenantId)
                    .ToListAsync();

                DbContext.Reports.RemoveRange(reports);
                await DbContext.SaveChangesAsync();

                // Clean up CosmosDB and Blob Storage would happen here
                // For now, the mock services handle cleanup
            }
            catch (Exception ex)
            {
                // Log cleanup errors but don't fail the test
                Console.WriteLine($"Cleanup error: {ex.Message}");
            }
        }

        public virtual void Dispose()
        {
            CleanupTestDataAsync().Wait();
            DbContext?.Dispose();
            ServiceProvider?.GetService<IServiceScope>()?.Dispose();
        }
    }

    /// <summary>
    /// Mock implementation of IReportStyleService for testing
    /// </summary>
    public class MockReportStyleService : IReportStyleService
    {
        private readonly Dictionary<string, ReportStyleDocument> _styles = new();

        public Task<ReportStyleDocument?> GetStyleAsync(string styleDocumentId, Guid tenantId, CancellationToken cancellationToken = default)
        {
            _styles.TryGetValue(styleDocumentId, out var style);
            return Task.FromResult(style);
        }

        public Task<ReportStyleDocument?> GetReportStyleAsync(Guid reportVersionId, Guid tenantId, CancellationToken cancellationToken = default)
        {
            var styleId = ReportStyleDocument.CreateReportStyleId(reportVersionId);
            return GetStyleAsync(styleId, tenantId, cancellationToken);
        }

        public Task<ReportStyleDocument?> GetTemplateStyleAsync(Guid templateId, Guid tenantId, CancellationToken cancellationToken = default)
        {
            var styleId = ReportStyleDocument.CreateTemplateStyleId(templateId);
            return GetStyleAsync(styleId, tenantId, cancellationToken);
        }

        public Task<string> SaveStyleAsync(ReportStyleDocument style, CancellationToken cancellationToken = default)
        {
            _styles[style.Id] = style;
            return Task.FromResult(style.Id);
        }

        public Task<bool> UpdateStyleAsync(ReportStyleDocument style, CancellationToken cancellationToken = default)
        {
            if (_styles.ContainsKey(style.Id))
            {
                _styles[style.Id] = style;
                return Task.FromResult(true);
            }
            return Task.FromResult(false);
        }

        public Task<bool> DeleteStyleAsync(string styleDocumentId, Guid tenantId, CancellationToken cancellationToken = default)
        {
            return Task.FromResult(_styles.Remove(styleDocumentId));
        }

        public Task<List<ReportStyleDocument>> GetStylesByTenantAsync(Guid tenantId, string? styleType = null, CancellationToken cancellationToken = default)
        {
            var styles = _styles.Values
                .Where(s => s.TenantId == tenantId)
                .Where(s => styleType == null || s.StyleType == styleType)
                .ToList();
            return Task.FromResult(styles);
        }

        public Task<string> CopyStyleForReportVersionAsync(string sourceStyleId, Guid targetReportVersionId, Guid tenantId, Guid userId, CancellationToken cancellationToken = default)
        {
            if (_styles.TryGetValue(sourceStyleId, out var sourceStyle))
            {
                var newStyle = new ReportStyleDocument
                {
                    Id = ReportStyleDocument.CreateReportStyleId(targetReportVersionId),
                    PartitionKey = tenantId.ToString(),
                    ReportVersionId = targetReportVersionId,
                    TenantId = tenantId,
                    StyleType = "report",
                    HtmlContent = sourceStyle.HtmlContent,
                    CssStyles = sourceStyle.CssStyles,
                    CreatedBy = userId,
                    LastModifiedBy = userId
                };
                _styles[newStyle.Id] = newStyle;
                return Task.FromResult(newStyle.Id);
            }
            throw new InvalidOperationException("Source style not found");
        }

        public Task<string> CreateStyleFromTemplateAsync(Guid templateId, Guid reportVersionId, Guid tenantId, Guid userId, CancellationToken cancellationToken = default)
        {
            var templateStyleId = ReportStyleDocument.CreateTemplateStyleId(templateId);
            return CopyStyleForReportVersionAsync(templateStyleId, reportVersionId, tenantId, userId, cancellationToken);
        }

        public Task<List<ReportStyleDocument>> SearchStylesAsync(Guid tenantId, string searchTerm, CancellationToken cancellationToken = default)
        {
            var styles = _styles.Values
                .Where(s => s.TenantId == tenantId)
                .Where(s => s.Metadata.Tags.Any(t => t.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)))
                .ToList();
            return Task.FromResult(styles);
        }

        public Task<List<ReportStyleDocument>> GetStylesByFrameworkAsync(Guid tenantId, string framework, CancellationToken cancellationToken = default)
        {
            var styles = _styles.Values
                .Where(s => s.TenantId == tenantId)
                .Where(s => s.Metadata.Framework.Equals(framework, StringComparison.OrdinalIgnoreCase))
                .ToList();
            return Task.FromResult(styles);
        }

        public Task<StyleValidationResult> ValidateStyleAsync(ReportStyleDocument style)
        {
            var result = StyleValidationResult.Success();
            if (string.IsNullOrEmpty(style.Id))
                result = StyleValidationResult.Failure("Style ID is required");
            return Task.FromResult(result);
        }
    }

    /// <summary>
    /// Mock implementation of IReportDataBlobService for testing
    /// </summary>
    public class MockReportDataBlobService : IReportDataBlobService
    {
        private readonly Dictionary<string, string> _blobs = new();

        public Task<Dictionary<string, object>> GetReportDataAsync(string blobPath, CancellationToken cancellationToken = default)
        {
            if (_blobs.TryGetValue(blobPath, out var json))
            {
                var data = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(json);
                return Task.FromResult(data ?? new Dictionary<string, object>());
            }
            return Task.FromResult(new Dictionary<string, object>());
        }

        public Task<Stream> GetReportDataStreamAsync(string blobPath, CancellationToken cancellationToken = default)
        {
            if (_blobs.TryGetValue(blobPath, out var json))
            {
                var stream = new MemoryStream(System.Text.Encoding.UTF8.GetBytes(json));
                return Task.FromResult<Stream>(stream);
            }
            return Task.FromResult<Stream>(new MemoryStream());
        }

        public Task<string> GetReportDataJsonAsync(string blobPath, CancellationToken cancellationToken = default)
        {
            _blobs.TryGetValue(blobPath, out var json);
            return Task.FromResult(json ?? "{}");
        }

        public Task<string> SaveReportDataAsync(Guid tenantId, Guid reportId, Guid versionId, Dictionary<string, object> data, CancellationToken cancellationToken = default)
        {
            var blobPath = GenerateReportDataPath(tenantId, reportId, versionId);
            var json = System.Text.Json.JsonSerializer.Serialize(data);
            _blobs[blobPath] = json;
            return Task.FromResult(blobPath);
        }

        public Task<string> SaveReportDataStreamAsync(Guid tenantId, Guid reportId, Guid versionId, Stream dataStream, string contentType = "application/json", CancellationToken cancellationToken = default)
        {
            var blobPath = GenerateReportDataPath(tenantId, reportId, versionId);
            using var reader = new StreamReader(dataStream);
            var json = reader.ReadToEnd();
            _blobs[blobPath] = json;
            return Task.FromResult(blobPath);
        }

        public Task<string> SaveReportDataJsonAsync(Guid tenantId, Guid reportId, Guid versionId, string jsonData, CancellationToken cancellationToken = default)
        {
            var blobPath = GenerateReportDataPath(tenantId, reportId, versionId);
            _blobs[blobPath] = jsonData;
            return Task.FromResult(blobPath);
        }

        public Task<bool> UpdateReportDataAsync(string blobPath, Dictionary<string, object> data, CancellationToken cancellationToken = default)
        {
            if (_blobs.ContainsKey(blobPath))
            {
                var json = System.Text.Json.JsonSerializer.Serialize(data);
                _blobs[blobPath] = json;
                return Task.FromResult(true);
            }
            return Task.FromResult(false);
        }

        public Task<bool> DeleteReportDataAsync(string blobPath, CancellationToken cancellationToken = default)
        {
            return Task.FromResult(_blobs.Remove(blobPath));
        }

        public Task<bool> ExistsAsync(string blobPath, CancellationToken cancellationToken = default)
        {
            return Task.FromResult(_blobs.ContainsKey(blobPath));
        }

        public Task<long?> GetSizeAsync(string blobPath, CancellationToken cancellationToken = default)
        {
            if (_blobs.TryGetValue(blobPath, out var json))
            {
                return Task.FromResult<long?>(System.Text.Encoding.UTF8.GetByteCount(json));
            }
            return Task.FromResult<long?>(null);
        }

        public Task<BlobMetadata?> GetMetadataAsync(string blobPath, CancellationToken cancellationToken = default)
        {
            if (_blobs.ContainsKey(blobPath))
            {
                return Task.FromResult<BlobMetadata?>(new BlobMetadata
                {
                    BlobPath = blobPath,
                    Size = System.Text.Encoding.UTF8.GetByteCount(_blobs[blobPath]),
                    ContentType = "application/json",
                    CreatedAt = DateTime.UtcNow,
                    LastModified = DateTime.UtcNow
                });
            }
            return Task.FromResult<BlobMetadata?>(null);
        }

        public Task<string> CopyReportDataAsync(string sourceBlobPath, Guid tenantId, Guid reportId, Guid versionId, CancellationToken cancellationToken = default)
        {
            if (_blobs.TryGetValue(sourceBlobPath, out var json))
            {
                var newBlobPath = GenerateReportDataPath(tenantId, reportId, versionId);
                _blobs[newBlobPath] = json;
                return Task.FromResult(newBlobPath);
            }
            throw new InvalidOperationException("Source blob not found");
        }

        public Task<List<string>> ListReportDataAsync(Guid tenantId, CancellationToken cancellationToken = default)
        {
            var paths = _blobs.Keys
                .Where(k => k.StartsWith($"{tenantId}/"))
                .ToList();
            return Task.FromResult(paths);
        }

        public Task<List<string>> ListReportVersionDataAsync(Guid tenantId, Guid reportId, CancellationToken cancellationToken = default)
        {
            var paths = _blobs.Keys
                .Where(k => k.StartsWith($"{tenantId}/reports/{reportId}/"))
                .ToList();
            return Task.FromResult(paths);
        }

        public Task<string> SaveAssetAsync(Guid tenantId, Guid reportId, Guid versionId, string assetName, Stream assetStream, string contentType, CancellationToken cancellationToken = default)
        {
            var blobPath = GenerateAssetPath(tenantId, reportId, versionId, assetName);
            using var reader = new StreamReader(assetStream);
            var content = reader.ReadToEnd();
            _blobs[blobPath] = content;
            return Task.FromResult(blobPath);
        }

        public Task<Stream> GetAssetAsync(string blobPath, CancellationToken cancellationToken = default)
        {
            if (_blobs.TryGetValue(blobPath, out var content))
            {
                var stream = new MemoryStream(System.Text.Encoding.UTF8.GetBytes(content));
                return Task.FromResult<Stream>(stream);
            }
            return Task.FromResult<Stream>(new MemoryStream());
        }

        public Task<bool> DeleteAssetAsync(string blobPath, CancellationToken cancellationToken = default)
        {
            return Task.FromResult(_blobs.Remove(blobPath));
        }

        public string GenerateReportDataPath(Guid tenantId, Guid reportId, Guid versionId)
        {
            return $"{tenantId}/reports/{reportId}/versions/{versionId}/data.json";
        }

        public string GenerateAssetPath(Guid tenantId, Guid reportId, Guid versionId, string assetName)
        {
            return $"{tenantId}/reports/{reportId}/versions/{versionId}/assets/{assetName}";
        }
    }
}