# SaaS Template: ASP.NET Core API & Next.js Frontend

A reusable, robust template for building Software-as-a-Service (SaaS) applications with a modern stack:

- **Backend**: ASP.NET Core 8 Web API
- **Frontend**: Next.js (App Router, TypeScript, Tailwind CSS)
- **Authentication**: ASP.NET Core Identity + JWT
- **Multi-tenancy**: Finbuckle.MultiTenant

## Project Overview

This template provides a comprehensive starting point for building SaaS applications with separate frontend and backend components:

### Backend (ASP.NET Core API)
- ASP.NET Core 8 Web API with Entity Framework Core
- SQL Server database access
- ASP.NET Core Identity for user management
- JWT-based authentication
- Finbuckle.MultiTenant for multi-tenancy support
- Swagger documentation

### Frontend (Next.js)
- Next.js with TypeScript and Tailwind CSS
- App Router with layouts and route groups
- Authentication context and route protection via middleware
- Backend-For-Frontend API routes to bridge frontend and backend

## Getting Started

### Prerequisites
- .NET 8 SDK
- SQL Server (LocalDB works for development)
- Node.js 18+ and npm

### Backend Setup
1. Navigate to `FY.WB.CSHero2` directory
2. Run `dotnet build` to build the backend
3. Run `dotnet run` to start the API (will run on http://localhost:5056 by default)

### Frontend Setup
1. Navigate to `FY.WB.CSHero2.UI` directory
2. Run `npm install` to install dependencies
3. Run `npm run dev` to start the development server (will run on http://localhost:3000)

## Key Features

- User registration and login with JWT authentication
- Multi-tenancy support with Finbuckle
- Protected routes in both frontend and backend
- Role-based authorization
- Distinct layouts for public, client, and tenant areas
- Comprehensive API communication patterns

## Current Status

This is an ongoing project with the following components completed:
- Basic ASP.NET Core API setup with authentication and tenant structure
- Next.js frontend with layouts and authentication flow
- Initial integration between frontend and backend

## Future Plans

- Complete the multi-tenant DbContext integration (currently blocked by CS0308 issue)
- Enhance user interface components
- Add comprehensive seeding
- Implement tenant-specific UI customization
- Develop user profile management
