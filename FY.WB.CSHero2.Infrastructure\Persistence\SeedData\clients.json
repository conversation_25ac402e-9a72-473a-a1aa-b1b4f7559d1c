[{"Id": "E44DBCCA-8DC0-4BBE-CBDF-08DD999C7247", "Name": "<PERSON>", "Email": "<EMAIL>", "Status": "Active", "CompanyName": "Tech Startup Inc", "CreatedAt": "2024-01-15T08:00:00Z", "UpdatedAt": "2024-02-20T15:30:00Z", "Phone": "+****************", "Address": "123 Innovation Street, Silicon Valley, CA", "CompanySize": "10-50", "Industry": "Software Development", "TenantId": "********-0000-0000-0003-************"}, {"Id": "9E073DE1-E923-40A3-CBE0-08DD999C7247", "Name": "<PERSON>", "Email": "<EMAIL>", "Status": "Active", "CompanyName": "CloudTech Solutions", "CreatedAt": "2024-01-20T09:15:00Z", "UpdatedAt": "2024-02-19T14:20:00Z", "Phone": "+****************", "Address": "456 Cloud Ave, San Francisco, CA", "CompanySize": "50-100", "Industry": "Cloud Computing", "TenantId": "********-0000-0000-0003-************"}, {"Id": "BA03FAE6-BF53-4A48-CBE1-08DD999C7247", "Name": "<PERSON>", "Email": "<EMAIL>", "Status": "Active", "CompanyName": "AI Research Labs", "CreatedAt": "2024-01-25T10:30:00Z", "UpdatedAt": "2024-02-18T16:45:00Z", "Phone": "+****************", "Address": "789 AI Blvd, San Jose, CA", "CompanySize": "100-500", "Industry": "Artificial Intelligence", "TenantId": "********-0000-0000-0003-************"}, {"Id": "33217F71-CFBF-470C-CBE2-08DD999C7247", "Name": "<PERSON>", "Email": "<EMAIL>", "Status": "Active", "CompanyName": "MedClinic Center", "CreatedAt": "2024-01-30T11:45:00Z", "UpdatedAt": "2024-02-17T13:15:00Z", "Phone": "+****************", "Address": "321 Healthcare Drive, Chicago, IL", "CompanySize": "100-250", "Industry": "Healthcare", "TenantId": "********-0000-0000-0003-********0002"}, {"Id": "88861946-6193-46DC-CBE3-08DD999C7247", "Name": "<PERSON>", "Email": "<EMAIL>", "Status": "Active", "CompanyName": "Care Services Group", "CreatedAt": "2024-02-01T13:00:00Z", "UpdatedAt": "2024-02-16T17:30:00Z", "Phone": "+****************", "Address": "654 Care Street, Chicago, IL", "CompanySize": "250-500", "Industry": "Healthcare Services", "TenantId": "********-0000-0000-0003-********0002"}, {"Id": "16C03B67-61A8-4578-CBE4-08DD999C7247", "Name": "<PERSON>", "Email": "<EMAIL>", "Status": "Active", "CompanyName": "Research Labs Co", "CreatedAt": "2024-02-05T14:15:00Z", "UpdatedAt": "2024-02-15T18:45:00Z", "Phone": "+****************", "Address": "987 Research Park, Otherville, NY", "CompanySize": "50-100", "Industry": "Research & Development", "TenantId": "********-0000-0000-0003-********0004"}, {"Id": "BCCAB48B-45E1-4D82-CBE5-08DD999C7247", "Name": "<PERSON>", "Email": "l.and<PERSON>@innovatetech.com", "Status": "Active", "CompanyName": "Innovate Technologies", "CreatedAt": "2024-02-10T15:30:00Z", "UpdatedAt": "2024-02-14T19:00:00Z", "Phone": "+****************", "Address": "753 Innovation Way, Otherville, NY", "CompanySize": "100-250", "Industry": "Technology Innovation", "TenantId": "********-0000-0000-0003-********0004"}, {"Id": "A1B2C3D4-E5F6-7890-1234-567890ABCDEF", "Name": "<PERSON>", "Email": "<EMAIL>", "Status": "Active", "CompanyName": "Global Finance Corp", "CreatedAt": "2024-02-12T09:00:00Z", "UpdatedAt": "2024-02-28T10:15:00Z", "Phone": "+****************", "Address": "100 Wall Street, New York, NY", "CompanySize": "500-1000", "Industry": "Financial Services", "TenantId": "********-0000-0000-0003-************"}, {"Id": "B2C3D4E5-F6G7-8901-2345-678901BCDEFG", "Name": "<PERSON>", "Email": "<EMAIL>", "Status": "Active", "CompanyName": "Investment Bank LLC", "CreatedAt": "2024-02-14T11:30:00Z", "UpdatedAt": "2024-03-01T14:45:00Z", "Phone": "+****************", "Address": "200 Financial Plaza, New York, NY", "CompanySize": "1000+", "Industry": "Investment Banking", "TenantId": "********-0000-0000-0003-************"}, {"Id": "C3D4E5F6-G7H8-9012-3456-789012CDEFGH", "Name": "<PERSON>", "Email": "<EMAIL>", "Status": "Active", "CompanyName": "Wealth Management Pro", "CreatedAt": "2024-02-16T13:45:00Z", "UpdatedAt": "2024-03-02T16:20:00Z", "Phone": "+****************", "Address": "300 Wealth Avenue, New York, NY", "CompanySize": "100-250", "Industry": "Wealth Management", "TenantId": "********-0000-0000-0003-************"}, {"Id": "D4E5F6G7-H8I9-0123-4567-890123DEFGHI", "Name": "<PERSON>", "Email": "<EMAIL>", "Status": "Active", "CompanyName": "BioTech Innovations", "CreatedAt": "2024-02-18T08:15:00Z", "UpdatedAt": "2024-03-03T11:30:00Z", "Phone": "+****************", "Address": "400 Biotech Drive, Boston, MA", "CompanySize": "250-500", "Industry": "Biotechnology", "TenantId": "********-0000-0000-0003-************"}, {"Id": "E5F6G7H8-I9J0-1234-5678-901234EFGHIJ", "Name": "<PERSON>", "Email": "<EMAIL>", "Status": "Active", "CompanyName": "Pharma Research Inc", "CreatedAt": "2024-02-20T10:00:00Z", "UpdatedAt": "2024-03-04T13:15:00Z", "Phone": "+****************", "Address": "500 Pharma Boulevard, Boston, MA", "CompanySize": "500-1000", "Industry": "Pharmaceutical", "TenantId": "********-0000-0000-0003-************"}, {"Id": "F6G7H8I9-J0K1-2345-6789-012345FGHIJK", "Name": "<PERSON>", "Email": "<EMAIL>", "Status": "Active", "CompanyName": "Medical Device Solutions", "CreatedAt": "2024-02-22T12:30:00Z", "UpdatedAt": "2024-03-05T15:45:00Z", "Phone": "+****************", "Address": "600 Medical Way, Chicago, IL", "CompanySize": "100-250", "Industry": "Medical Devices", "TenantId": "********-0000-0000-0003-********0002"}, {"Id": "G7H8I9J0-K1L2-3456-7890-123456GHIJKL", "Name": "<PERSON>", "Email": "<EMAIL>", "Status": "Active", "CompanyName": "Health Systems Network", "CreatedAt": "2024-02-24T14:00:00Z", "UpdatedAt": "2024-03-06T17:30:00Z", "Phone": "+****************", "Address": "700 Health Plaza, Chicago, IL", "CompanySize": "1000+", "Industry": "Healthcare Systems", "TenantId": "********-0000-0000-0003-********0002"}, {"Id": "H8I9J0K1-L2M3-4567-8901-234567<PERSON><PERSON><PERSON><PERSON><PERSON>", "Name": "<PERSON>", "Email": "<EMAIL>", "Status": "Active", "CompanyName": "Quantum Computing Corp", "CreatedAt": "2024-02-26T16:15:00Z", "UpdatedAt": "2024-03-07T19:00:00Z", "Phone": "+****************", "Address": "800 Quantum Street, Otherville, NY", "CompanySize": "50-100", "Industry": "Quantum Computing", "TenantId": "********-0000-0000-0003-********0004"}, {"Id": "I9J0K1L2-M3N4-5678-9012-345678IJKLMN", "Name": "<PERSON>", "Email": "<EMAIL>", "Status": "Active", "CompanyName": "Robotics Future Labs", "CreatedAt": "2024-02-28T18:30:00Z", "UpdatedAt": "2024-03-08T21:15:00Z", "Phone": "+****************", "Address": "900 Robotics Avenue, Otherville, NY", "CompanySize": "100-250", "Industry": "Robotics", "TenantId": "********-0000-0000-0003-********0004"}, {"Id": "J0K1L2M3-N4O5-6789-0123-456789JKLMNO", "Name": "<PERSON>", "Email": "<EMAIL>", "Status": "Active", "CompanyName": "CyberSecurity Pro", "CreatedAt": "2024-03-01T20:00:00Z", "UpdatedAt": "2024-03-09T23:30:00Z", "Phone": "+****************", "Address": "1000 Security Boulevard, San Francisco, CA", "CompanySize": "250-500", "Industry": "Cybersecurity", "TenantId": "********-0000-0000-0003-************"}, {"Id": "K1L2M3N4-O5P6-7890-1234-567890KLMNOP", "Name": "<PERSON>", "Email": "<EMAIL>", "Status": "Active", "CompanyName": "Data Analytics Solutions", "CreatedAt": "2024-03-03T07:45:00Z", "UpdatedAt": "2024-03-10T10:00:00Z", "Phone": "+****************", "Address": "1100 Data Drive, San Francisco, CA", "CompanySize": "100-250", "Industry": "Data Analytics", "TenantId": "********-0000-0000-0003-************"}, {"Id": "L2M3N4O5-P6Q7-8901-2345-678901LMNOPQ", "Name": "<PERSON>", "Email": "<EMAIL>", "Status": "Active", "CompanyName": "InsuranceTech Innovations", "CreatedAt": "2024-03-05T09:30:00Z", "UpdatedAt": "2024-03-11T12:45:00Z", "Phone": "+****************", "Address": "1200 Insurance Way, New York, NY", "CompanySize": "500-1000", "Industry": "Insurance Technology", "TenantId": "********-0000-0000-0003-************"}, {"Id": "M3N4O5P6-Q7R8-9012-3456-789012MNOPQR", "Name": "<PERSON>", "Email": "<EMAIL>", "Status": "Active", "CompanyName": "FinTech Startup Hub", "CreatedAt": "2024-03-07T11:15:00Z", "UpdatedAt": "2024-03-12T14:30:00Z", "Phone": "+****************", "Address": "1300 FinTech Plaza, New York, NY", "CompanySize": "10-50", "Industry": "Financial Technology", "TenantId": "********-0000-0000-0003-************"}]