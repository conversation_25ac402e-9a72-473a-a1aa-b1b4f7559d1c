# Verify Seeding Fix - Simple verification script
Write-Host "=== Verifying Seeding Fix ===" -ForegroundColor Cyan

# Run diagnostic check
Write-Host "`nRunning diagnostic check..." -ForegroundColor Yellow
$diagnosticResult = powershell -ExecutionPolicy Bypass -File diagnose-seed-data-fk-issues.ps1 2>&1
$diagnosticOutput = $diagnosticResult | Out-String

if ($diagnosticOutput -match "0 reports have invalid client references" -and 
    $diagnosticOutput -match "0 report versions will fail FK constraints" -and 
    $diagnosticOutput -match "0 report sections will fail FK constraints") {
    Write-Host "✓ All foreign key issues have been resolved!" -ForegroundColor Green
} else {
    Write-Host "✗ Some foreign key issues may still exist" -ForegroundColor Red
    exit 1
}

# Test project build
Write-Host "`nTesting project build..." -ForegroundColor Yellow
Set-Location "FY.WB.CSHero2"
$buildResult = dotnet build --configuration Debug --verbosity quiet
if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Project build successful" -ForegroundColor Green
} else {
    Write-Host "✗ Project build failed" -ForegroundColor Red
    Set-Location ".."
    exit 1
}
Set-Location ".."

Write-Host "`n🎉 Verification completed successfully!" -ForegroundColor Green
Write-Host "Your seeding issues have been resolved. Key fixes:" -ForegroundColor Cyan
Write-Host "- Fixed 12 client ID mismatches in reports.json" -ForegroundColor White
Write-Host "- Added FK validation to ReportVersions and ReportSections seeding" -ForegroundColor White
Write-Host "- Cleaned up orphaned data" -ForegroundColor White
Write-Host "`nYour dev server build should now work without FK constraint errors." -ForegroundColor Green
