# PowerShell script to fix invalid GUIDs in reports.json
param(
    [string]$JsonPath = "FY.WB.CSHero2.Infrastructure\Persistence\SeedData\reports.json"
)

Write-Host "Starting GUID validation and fix for reports.json..." -ForegroundColor Green

# Check if file exists
if (-not (Test-Path $JsonPath)) {
    Write-Error "File not found: $JsonPath"
    exit 1
}

# Create backup
$backupPath = $JsonPath + ".backup." + (Get-Date -Format "yyyyMMdd_HHmmss")
Copy-Item $JsonPath $backupPath
Write-Host "Backup created: $backupPath" -ForegroundColor Yellow

try {
    # Read and parse JSON
    $jsonContent = Get-Content $JsonPath -Raw
    $reports = $jsonContent | ConvertFrom-Json
    
    Write-Host "Found $($reports.Count) reports in JSON file" -ForegroundColor Cyan
    
    # Function to validate GUID format
    function Test-ValidGuid {
        param([string]$guidString)
        try {
            [System.Guid]::Parse($guidString) | Out-Null
            return $true
        }
        catch {
            return $false
        }
    }
    
    # Function to generate new GUID
    function New-ValidGuid {
        return [System.Guid]::NewGuid().ToString()
    }
    
    $fixedCount = 0
    $invalidGuids = @()
    
    # Check and fix each report
    for ($i = 0; $i -lt $reports.Count; $i++) {
        $report = $reports[$i]
        $hasInvalidGuid = $false
        
        # Check id field
        if (-not (Test-ValidGuid $report.id)) {
            Write-Host "Invalid id GUID at index $i`: $($report.id)" -ForegroundColor Red
            $newGuid = New-ValidGuid
            $invalidGuids += @{
                Index = $i
                Field = "id"
                OldValue = $report.id
                NewValue = $newGuid
            }
            $report.id = $newGuid
            $hasInvalidGuid = $true
        }
        
        # Check clientId field
        if (-not (Test-ValidGuid $report.clientId)) {
            Write-Host "Invalid clientId GUID at index $i`: $($report.clientId)" -ForegroundColor Red
            $newGuid = New-ValidGuid
            $invalidGuids += @{
                Index = $i
                Field = "clientId"
                OldValue = $report.clientId
                NewValue = $newGuid
            }
            $report.clientId = $newGuid
            $hasInvalidGuid = $true
        }
        
        # Check tenantId field
        if (-not (Test-ValidGuid $report.tenantId)) {
            Write-Host "Invalid tenantId GUID at index $i`: $($report.tenantId)" -ForegroundColor Red
            $newGuid = New-ValidGuid
            $invalidGuids += @{
                Index = $i
                Field = "tenantId"
                OldValue = $report.tenantId
                NewValue = $newGuid
            }
            $report.tenantId = $newGuid
            $hasInvalidGuid = $true
        }
        
        if ($hasInvalidGuid) {
            $fixedCount++
        }
    }
    
    if ($invalidGuids.Count -eq 0) {
        Write-Host "No invalid GUIDs found. All GUIDs are properly formatted." -ForegroundColor Green
    }
    else {
        Write-Host "`nFixed $fixedCount reports with invalid GUIDs:" -ForegroundColor Yellow
        
        # Display summary of changes
        foreach ($change in $invalidGuids) {
            Write-Host "  Index $($change.Index) - $($change.Field): $($change.OldValue) -> $($change.NewValue)" -ForegroundColor Cyan
        }
        
        # Convert back to JSON and save
        $newJsonContent = $reports | ConvertTo-Json -Depth 10
        Set-Content -Path $JsonPath -Value $newJsonContent -Encoding UTF8
        
        Write-Host "`nUpdated $JsonPath with valid GUIDs" -ForegroundColor Green
        
        # Verify the fix by trying to parse again
        Write-Host "`nVerifying fix..." -ForegroundColor Yellow
        $verifyContent = Get-Content $JsonPath -Raw
        $verifyReports = $verifyContent | ConvertFrom-Json
        
        $allValid = $true
        for ($i = 0; $i -lt $verifyReports.Count; $i++) {
            $report = $verifyReports[$i]
            if (-not (Test-ValidGuid $report.id) -or -not (Test-ValidGuid $report.clientId) -or -not (Test-ValidGuid $report.tenantId)) {
                Write-Host "Verification failed at index $i" -ForegroundColor Red
                $allValid = $false
            }
        }
        
        if ($allValid) {
            Write-Host "Verification successful! All GUIDs are now valid." -ForegroundColor Green
        }
        else {
            Write-Host "Verification failed! Some GUIDs are still invalid." -ForegroundColor Red
            exit 1
        }
    }
    
    Write-Host "`nGUID fix completed successfully!" -ForegroundColor Green
    Write-Host "Backup saved as: $backupPath" -ForegroundColor Yellow
}
catch {
    Write-Error "Error processing JSON file: $($_.Exception.Message)"
    Write-Host "Restoring from backup..." -ForegroundColor Yellow
    Copy-Item $backupPath $JsonPath -Force
    exit 1
}
