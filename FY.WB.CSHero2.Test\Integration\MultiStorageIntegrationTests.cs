using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Azure.Cosmos;
using Azure.Storage.Blobs;
using Xunit;
using FY.WB.CSHero2.Application.Common.Interfaces;
using FY.WB.CSHero2.Infrastructure.Persistence.Repositories;
using FY.WB.CSHero2.Application.Models.MultiStorage;

namespace FY.WB.CSHero2.Test.Integration
{
    /// <summary>
    /// Integration tests for the multi-storage architecture
    /// Tests the interaction between Cosmos DB and Blob Storage repositories
    /// </summary>
    public class MultiStorageIntegrationTests : IClassFixture<MultiStorageTestFixture>
    {
        private readonly MultiStorageTestFixture _fixture;
        private readonly IReportDataRepository _cosmosRepository;
        private readonly IReportComponentsRepository _blobRepository;

        public MultiStorageIntegrationTests(MultiStorageTestFixture fixture)
        {
            _fixture = fixture;
            _cosmosRepository = _fixture.ServiceProvider.GetRequiredService<IReportDataRepository>();
            _blobRepository = _fixture.ServiceProvider.GetRequiredService<IReportComponentsRepository>();
        }

        [Fact]
        public async Task CosmosDb_CreateAndRetrieveReportData_ShouldSucceed()
        {
            // Arrange
            var reportData = new ReportData
            {
                Id = $"test-report-{Guid.NewGuid()}",
                ReportId = Guid.NewGuid().ToString(),
                VersionId = Guid.NewGuid().ToString(),
                VersionNumber = 1,
                TenantId = "test-tenant",
                Sections = new List<ReportSection>
                {
                    new ReportSection
                    {
                        Id = Guid.NewGuid().ToString(),
                        Title = "Test Section",
                        Type = "text",
                        Order = 1,
                        Fields = new List<ReportSectionField>
                        {
                            new ReportSectionField
                            {
                                Id = Guid.NewGuid().ToString(),
                                Name = "TestField",
                                Type = "text",
                                Content = "Test content",
                                Order = 1
                            }
                        }
                    }
                },
                Metadata = new ReportDataMetadata
                {
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = "test-user",
                    LastModifiedAt = DateTime.UtcNow,
                    LastModifiedBy = "test-user",
                    Version = 1
                }
            };

            // Act
            var documentId = await _cosmosRepository.CreateReportDataAsync(reportData);
            var retrievedData = await _cosmosRepository.GetReportDataAsync(documentId, "test-tenant");

            // Assert
            Assert.NotNull(retrievedData);
            Assert.Equal(reportData.ReportId, retrievedData.ReportId);
            Assert.Equal(reportData.VersionId, retrievedData.VersionId);
            Assert.Single(retrievedData.Sections);
            Assert.Equal("Test Section", retrievedData.Sections.First().Title);
            Assert.Single(retrievedData.Sections.First().Fields);
            Assert.Equal("TestField", retrievedData.Sections.First().Fields.First().Name);

            // Cleanup
            await _cosmosRepository.DeleteReportDataAsync(documentId, "test-tenant");
        }

        [Fact]
        public async Task BlobStorage_SaveAndRetrieveComponents_ShouldSucceed()
        {
            // Arrange
            var reportId = Guid.NewGuid();
            var versionId = Guid.NewGuid();
            var components = new List<ReportComponent>
            {
                new ReportComponent
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = "TestComponent",
                    SectionId = "section-1",
                    Code = @"
import React from 'react';

interface TestComponentProps {
    title: string;
    content: string;
}

const TestComponent: React.FC<TestComponentProps> = ({ title, content }) => {
    return (
        <div className=""test-component"">
            <h2>{title}</h2>
            <p>{content}</p>
        </div>
    );
};

export default TestComponent;
",
                    Imports = new List<string> { "import React from 'react';" },
                    Props = new List<string> { "title: string", "content: string" }
                }
            };

            // Act
            var blobId = await _blobRepository.SaveComponentsAsync(reportId, versionId, components);
            var metadata = await _blobRepository.GetComponentsMetadataAsync(blobId);
            var retrievedComponents = await _blobRepository.GetComponentsAsync(blobId);
            var singleComponent = await _blobRepository.GetComponentAsync(blobId, "TestComponent");

            // Assert
            Assert.NotNull(metadata);
            Assert.Equal(reportId.ToString(), metadata.ReportId);
            Assert.Equal(versionId.ToString(), metadata.VersionId);
            Assert.Single(metadata.Components);
            Assert.Equal("TestComponent", metadata.Components.First().Name);

            Assert.NotNull(retrievedComponents);
            Assert.Single(retrievedComponents);
            Assert.Equal("TestComponent", retrievedComponents.First().Name);
            Assert.Contains("React.FC<TestComponentProps>", retrievedComponents.First().Code);

            Assert.NotNull(singleComponent);
            Assert.Equal("TestComponent", singleComponent.Name);
            Assert.Equal("section-1", singleComponent.SectionId);

            // Cleanup
            await _blobRepository.DeleteComponentsAsync(blobId);
        }

        [Fact]
        public async Task CosmosDb_SectionOperations_ShouldSucceed()
        {
            // Arrange
            var reportData = new ReportData
            {
                Id = $"test-report-{Guid.NewGuid()}",
                ReportId = Guid.NewGuid().ToString(),
                VersionId = Guid.NewGuid().ToString(),
                VersionNumber = 1,
                TenantId = "test-tenant",
                Sections = new List<ReportSection>(),
                Metadata = new ReportDataMetadata
                {
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = "test-user",
                    LastModifiedAt = DateTime.UtcNow,
                    LastModifiedBy = "test-user",
                    Version = 1
                }
            };

            var documentId = await _cosmosRepository.CreateReportDataAsync(reportData);

            try
            {
                // Act - Add a section
                var newSection = new ReportSection
                {
                    Id = "new-section",
                    Title = "New Section",
                    Type = "chart",
                    Order = 1,
                    Fields = new List<ReportSectionField>
                    {
                        new ReportSectionField
                        {
                            Id = "chart-data",
                            Name = "chartData",
                            Type = "json",
                            Content = "{\"type\": \"bar\", \"data\": [1,2,3]}",
                            Order = 1
                        }
                    }
                };

                await _cosmosRepository.UpdateReportSectionAsync(documentId, newSection, "test-tenant");

                // Verify section was added
                var retrievedSection = await _cosmosRepository.GetReportSectionAsync(documentId, "new-section", "test-tenant");
                Assert.NotNull(retrievedSection);
                Assert.Equal("New Section", retrievedSection.Title);
                Assert.Equal("chart", retrievedSection.Type);
                Assert.Single(retrievedSection.Fields);

                // Act - Update section field
                var updatedField = new ReportSectionField
                {
                    Id = "chart-data",
                    Name = "chartData",
                    Type = "json",
                    Content = "{\"type\": \"line\", \"data\": [4,5,6]}",
                    Order = 1
                };

                await _cosmosRepository.UpdateReportSectionFieldAsync(documentId, "new-section", updatedField, "test-tenant");

                // Verify field was updated
                var retrievedField = await _cosmosRepository.GetReportSectionFieldAsync(documentId, "new-section", "chart-data", "test-tenant");
                Assert.NotNull(retrievedField);
                Assert.Contains("line", retrievedField.Content);
                Assert.Contains("[4,5,6]", retrievedField.Content);
            }
            finally
            {
                // Cleanup
                await _cosmosRepository.DeleteReportDataAsync(documentId, "test-tenant");
            }
        }

        [Fact]
        public async Task BlobStorage_ComponentUtilities_ShouldWork()
        {
            // Arrange
            var reportId = Guid.NewGuid();
            var versionId = Guid.NewGuid();
            var components = new List<ReportComponent>
            {
                new ReportComponent
                {
                    Id = "comp-1",
                    Name = "Header",
                    SectionId = "section-1",
                    Code = "const Header = () => <h1>Test</h1>;"
                },
                new ReportComponent
                {
                    Id = "comp-2",
                    Name = "Footer",
                    SectionId = "section-2",
                    Code = "const Footer = () => <footer>Test Footer</footer>;"
                }
            };

            var blobId = await _blobRepository.SaveComponentsAsync(reportId, versionId, components);

            try
            {
                // Act & Assert - Test utility methods
                var exists = await _blobRepository.ComponentsExistAsync(blobId);
                Assert.True(exists);

                var size = await _blobRepository.GetComponentsSizeAsync(blobId);
                Assert.True(size > 0);

                var storageResult = await _blobRepository.GetStorageResultAsync(blobId);
                Assert.NotNull(storageResult);
                Assert.True(storageResult.Success);
                Assert.Equal(2, storageResult.ComponentCount);
                Assert.Equal(blobId, storageResult.BlobId);

                var componentCode = await _blobRepository.GetComponentCodeAsync(blobId, "Header");
                Assert.Contains("Test", componentCode);
            }
            finally
            {
                // Cleanup
                await _blobRepository.DeleteComponentsAsync(blobId);
            }
        }
    }

    /// <summary>
    /// Test fixture for setting up multi-storage test environment
    /// </summary>
    public class MultiStorageTestFixture : IDisposable
    {
        public IServiceProvider ServiceProvider { get; private set; }
        private readonly ServiceProvider _serviceProvider;

        public MultiStorageTestFixture()
        {
            var services = new ServiceCollection();
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: true)
                .AddJsonFile("appsettings.Test.json", optional: true)
                .AddEnvironmentVariables()
                .Build();

            // Add logging
            services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Warning));

            // Add Azure clients (configured for test environment)
            services.AddSingleton(sp =>
            {
                var connectionString = configuration["CosmosDb:ConnectionString"];
                if (string.IsNullOrEmpty(connectionString))
                {
                    // Use Cosmos DB emulator for testing
                    connectionString = "AccountEndpoint=https://localhost:8081/;AccountKey=C2y6yDjf5/R+ob0N8A7Cgv30VRDJIWEHLM+4QDU5DE2nQ9nDuVTqobD4b8mGGyPMbIZnqyMsEcaGQy67XIw/Jw==";
                }
                return new CosmosClient(connectionString, new CosmosClientOptions
                {
                    SerializerOptions = new CosmosSerializationOptions
                    {
                        PropertyNamingPolicy = CosmosPropertyNamingPolicy.CamelCase
                    }
                });
            });

            services.AddSingleton(sp =>
            {
                var connectionString = configuration["BlobStorage:ConnectionString"];
                if (string.IsNullOrEmpty(connectionString))
                {
                    // Use Azurite for testing
                    connectionString = "DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10000/devstoreaccount1;";
                }
                return new BlobServiceClient(connectionString);
            });

            // Add repositories
            services.AddScoped<IReportDataRepository, ReportDataRepository>();
            services.AddScoped<IReportComponentsRepository, ReportComponentsRepository>();

            _serviceProvider = services.BuildServiceProvider();
            ServiceProvider = _serviceProvider;
        }

        public void Dispose()
        {
            _serviceProvider?.Dispose();
        }
    }
}