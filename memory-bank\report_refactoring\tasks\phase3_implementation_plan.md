# Phase 3: Multi-Storage Architecture Implementation Plan

## Overview

This document provides a comprehensive implementation plan for Phase 3 of the multi-storage architecture, transforming the current SQL-only implementation into a hybrid system leveraging SQL Database, Azure Cosmos DB, and Azure Blob Storage.

## Current State Assessment

### ✅ **COMPLETED WORK:**
1. **SQL Schema**: Report, ReportVersion, ReportSection, ReportSectionField, and ReportStyle entities implemented
2. **Multi-storage references**: `DataDocumentId` and `ComponentsBlobId` fields added to Report and ReportVersion entities
3. **Azure Infrastructure**: Cosmos DB account exists (`cshero-cosmosdb`), database `CSHeroReports` configured
4. **Database migrations**: Applied through migration `20250602224758_AddReportSectionEntities`
5. **DbContext**: Updated with all necessary DbSets including ReportStyles

### 🔄 **PHASE 3 IMPLEMENTATION TASKS:**

## Implementation Strategy

### **Approach: Infrastructure-First with Production Azure Resources**
- ✅ **Benefits**: Production-ready, scalable, follows Azure best practices
- 🎯 **Focus**: Real Azure Blob Storage instead of Azurite emulator
- 📋 **Priority**: Azure infrastructure setup → Repository implementation → Service orchestration → Data migration

## Detailed Implementation Plan

### **Phase 3.1: Azure Infrastructure Setup** (Day 1)

#### Task 3.1.1: Azure Blob Storage Account Creation
```bash
# Create Azure Storage Account in existing resource group
az storage account create \
  --name csheroblobstorage \
  --resource-group [existing-dev-resource-group] \
  --location [same-as-cosmos-db] \
  --sku Standard_LRS \
  --kind StorageV2 \
  --access-tier Hot

# Create containers for report components and data
az storage container create \
  --name report-components \
  --account-name csheroblobstorage \
  --public-access off

az storage container create \
  --name report-data \
  --account-name csheroblobstorage \
  --public-access off
```

#### Task 3.1.2: Cosmos DB Container Setup
```bash
# Create the Reports container in existing Cosmos DB
az cosmosdb sql container create \
  --account-name cshero-cosmosdb \
  --database-name CSHeroReports \
  --name Reports \
  --partition-key-path "/id" \
  --throughput 400 \
  --idx @indexing-policy.json
```

#### Task 3.1.3: Configuration Updates
```json
// Update appsettings.json
{
  "CosmosDb": {
    "ConnectionString": "AccountEndpoint=https://cshero-cosmosdb.documents.azure.com:443/;AccountKey=...",
    "DatabaseName": "CSHeroReports",
    "ContainerName": "Reports",
    "MaxRetryAttempts": 3,
    "RequestTimeoutSeconds": 30,
    "MaxConnections": 50
  },
  "BlobStorage": {
    "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=csheroblobstorage;AccountKey=...;EndpointSuffix=core.windows.net",
    "ContainerName": "report-components",
    "ReportDataContainer": "report-data",
    "MaxRetryAttempts": 3,
    "RequestTimeoutSeconds": 30,
    "MaxConcurrentOperations": 10,
    "EnableEncryption": true,
    "DefaultContentType": "application/json"
  }
}
```

### **Phase 3.2: Cosmos DB Models and Repository** (Days 1-2)

#### Task 3.2.1: Create Cosmos DB Models
```mermaid
classDiagram
    class ReportData {
        +string Id
        +string ReportId
        +string VersionId
        +int VersionNumber
        +List~CosmosReportSection~ Sections
        +ReportDataMetadata Metadata
    }
    
    class CosmosReportSection {
        +string Id
        +string Title
        +string Type
        +int Order
        +List~CosmosReportSectionField~ Fields
        +Dictionary~string,object~ Metadata
    }
    
    class CosmosReportSectionField {
        +string Id
        +string Name
        +string Type
        +string Content
        +int Order
        +Dictionary~string,object~ Metadata
    }
    
    class ReportDataMetadata {
        +DateTime CreatedAt
        +string CreatedBy
        +DateTime LastModifiedAt
        +string LastModifiedBy
    }
    
    ReportData --> CosmosReportSection
    ReportData --> ReportDataMetadata
    CosmosReportSection --> CosmosReportSectionField
```

**Files to Create:**
- `FY.WB.CSHero2.Domain/Models/CosmosDb/ReportData.cs`
- `FY.WB.CSHero2.Domain/Models/CosmosDb/CosmosReportSection.cs`
- `FY.WB.CSHero2.Domain/Models/CosmosDb/CosmosReportSectionField.cs`
- `FY.WB.CSHero2.Domain/Models/CosmosDb/ReportDataMetadata.cs`

#### Task 3.2.2: Implement Cosmos DB Repository
```csharp
// IReportDataRepository interface with comprehensive CRUD operations
public interface IReportDataRepository
{
    // Document-level operations
    Task<ReportData?> GetReportDataAsync(string documentId, CancellationToken cancellationToken = default);
    Task<ReportData?> GetReportDataByReportIdAsync(string reportId, string versionId, CancellationToken cancellationToken = default);
    Task<string> CreateReportDataAsync(ReportData data, CancellationToken cancellationToken = default);
    Task UpdateReportDataAsync(ReportData data, CancellationToken cancellationToken = default);
    Task DeleteReportDataAsync(string documentId, CancellationToken cancellationToken = default);
    
    // Section-level operations
    Task<CosmosReportSection?> GetSectionAsync(string documentId, string sectionId, CancellationToken cancellationToken = default);
    Task AddSectionAsync(string documentId, CosmosReportSection section, CancellationToken cancellationToken = default);
    Task UpdateSectionAsync(string documentId, CosmosReportSection section, CancellationToken cancellationToken = default);
    Task DeleteSectionAsync(string documentId, string sectionId, CancellationToken cancellationToken = default);
    
    // Field-level operations
    Task<CosmosReportSectionField?> GetFieldAsync(string documentId, string sectionId, string fieldId, CancellationToken cancellationToken = default);
    Task AddFieldAsync(string documentId, string sectionId, CosmosReportSectionField field, CancellationToken cancellationToken = default);
    Task UpdateFieldAsync(string documentId, string sectionId, CosmosReportSectionField field, CancellationToken cancellationToken = default);
    Task DeleteFieldAsync(string documentId, string sectionId, string fieldId, CancellationToken cancellationToken = default);
}
```

**Files to Create:**
- `FY.WB.CSHero2.Application/Common/Interfaces/IReportDataRepository.cs`
- `FY.WB.CSHero2.Infrastructure/Persistence/Repositories/ReportDataRepository.cs`

### **Phase 3.3: Blob Storage Models and Repository** (Days 2-3)

#### Task 3.3.1: Component Storage Architecture
```mermaid
graph TB
    subgraph "Azure Blob Storage Structure"
        A[reports/] --> B[{reportId}/]
        B --> C[{versionId}/]
        C --> D[metadata.json]
        C --> E[components/]
        E --> F[Component1.tsx]
        E --> G[Component2.tsx]
        E --> H[Component3.tsx]
        C --> I[assets/]
        I --> J[styles.css]
        I --> K[images/]
    end
```

#### Task 3.3.2: Create Blob Storage Models
```csharp
public class ComponentsMetadata
{
    public string ReportId { get; set; } = string.Empty;
    public string VersionId { get; set; } = string.Empty;
    public int VersionNumber { get; set; }
    public List<ComponentMetadata> Components { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class ComponentMetadata
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string SectionId { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public List<string> Imports { get; set; } = new();
    public List<string> Props { get; set; } = new();
}

public class ComponentDefinition
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string SectionId { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public List<string> Imports { get; set; } = new();
    public List<string> Props { get; set; } = new();
}
```

**Files to Create:**
- `FY.WB.CSHero2.Domain/Models/BlobStorage/ComponentsMetadata.cs`
- `FY.WB.CSHero2.Domain/Models/BlobStorage/ComponentMetadata.cs`
- `FY.WB.CSHero2.Domain/Models/BlobStorage/ComponentDefinition.cs`

#### Task 3.3.3: Implement Blob Storage Repository
```csharp
public interface IReportComponentsRepository
{
    Task<string> SaveComponentsAsync(Guid reportId, Guid versionId, IEnumerable<ComponentDefinition> components, CancellationToken cancellationToken = default);
    Task<ComponentsMetadata?> GetComponentsMetadataAsync(string blobId, CancellationToken cancellationToken = default);
    Task<ComponentDefinition?> GetComponentAsync(string blobId, string componentName, CancellationToken cancellationToken = default);
    Task<IEnumerable<ComponentDefinition>> GetAllComponentsAsync(string blobId, CancellationToken cancellationToken = default);
    Task DeleteComponentsAsync(string blobId, CancellationToken cancellationToken = default);
    Task<byte[]> ExportComponentsAsZipAsync(string blobId, CancellationToken cancellationToken = default);
    Task<Stream> GetComponentStreamAsync(string blobId, string componentName, CancellationToken cancellationToken = default);
    Task<bool> ComponentExistsAsync(string blobId, string componentName, CancellationToken cancellationToken = default);
}
```

**Files to Create:**
- `FY.WB.CSHero2.Application/Common/Interfaces/IReportComponentsRepository.cs`
- `FY.WB.CSHero2.Infrastructure/Persistence/Repositories/ReportComponentsRepository.cs`

### **Phase 3.4: Orchestration Services** (Days 3-4)

#### Task 3.4.1: Service Architecture
```mermaid
graph TB
    subgraph "Service Layer"
        A[ReportService] --> B[IReportDataRepository]
        A --> C[IReportComponentsRepository]
        A --> D[IApplicationDbContext]
        
        E[ReportDataService] --> B
        F[ReportRenderingService] --> C
        
        G[ReportMigrationService] --> A
        G --> E
        G --> F
    end
    
    subgraph "Storage Layer"
        B --> H[Azure Cosmos DB]
        C --> I[Azure Blob Storage]
        D --> J[SQL Database]
    end
    
    subgraph "Cross-Cutting Concerns"
        K[Logging] --> A
        K --> E
        K --> F
        L[Error Handling] --> A
        L --> E
        L --> F
        M[Caching] --> A
        M --> E
    end
```

#### Task 3.4.2: Create Service Interfaces
```csharp
public interface IReportService
{
    Task<ReportDto> GetReportAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task<IEnumerable<ReportDto>> GetReportsAsync(Guid? tenantId = null, CancellationToken cancellationToken = default);
    Task<Guid> CreateReportAsync(CreateReportDto request, CancellationToken cancellationToken = default);
    Task UpdateReportAsync(UpdateReportDto request, CancellationToken cancellationToken = default);
    Task DeleteReportAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task<ReportVersionDto> CreateVersionAsync(Guid reportId, string description, CancellationToken cancellationToken = default);
    Task<ReportStyleDto> GetReportStyleAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task UpdateReportStyleAsync(Guid reportId, ReportStyleDto style, CancellationToken cancellationToken = default);
}

public interface IReportDataService
{
    Task<ReportDataDto> GetReportDataAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task UpdateReportDataAsync(Guid reportId, ReportDataDto data, CancellationToken cancellationToken = default);
    Task<ReportSectionDto> GetSectionAsync(Guid reportId, string sectionId, CancellationToken cancellationToken = default);
    Task<ReportSectionDto> AddSectionAsync(Guid reportId, CreateReportSectionDto section, CancellationToken cancellationToken = default);
    Task UpdateSectionAsync(Guid reportId, UpdateReportSectionDto section, CancellationToken cancellationToken = default);
    Task DeleteSectionAsync(Guid reportId, string sectionId, CancellationToken cancellationToken = default);
    Task<ReportSectionFieldDto> AddFieldAsync(Guid reportId, string sectionId, CreateReportSectionFieldDto field, CancellationToken cancellationToken = default);
    Task UpdateFieldAsync(Guid reportId, string sectionId, UpdateReportSectionFieldDto field, CancellationToken cancellationToken = default);
    Task DeleteFieldAsync(Guid reportId, string sectionId, string fieldId, CancellationToken cancellationToken = default);
}

public interface IReportRenderingService
{
    Task<RenderResultDto> RenderReportAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task<ComponentDefinitionDto> GetComponentAsync(Guid reportId, string componentId, CancellationToken cancellationToken = default);
    Task<IEnumerable<ComponentDefinitionDto>> GetComponentsAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task<byte[]> ExportReportAsync(Guid reportId, ExportFormat format, CancellationToken cancellationToken = default);
}
```

**Files to Create:**
- `FY.WB.CSHero2.Application/Common/Interfaces/IReportService.cs`
- `FY.WB.CSHero2.Application/Common/Interfaces/IReportDataService.cs`
- `FY.WB.CSHero2.Application/Common/Interfaces/IReportRenderingService.cs`
- `FY.WB.CSHero2.Application/Reports/Services/ReportService.cs`
- `FY.WB.CSHero2.Application/Reports/Services/ReportDataService.cs`
- `FY.WB.CSHero2.Application/Reports/Services/ReportRenderingService.cs`

#### Task 3.4.3: Create DTOs
**Files to Create:**
- `FY.WB.CSHero2.Application/Reports/DTOs/ReportDto.cs`
- `FY.WB.CSHero2.Application/Reports/DTOs/CreateReportDto.cs`
- `FY.WB.CSHero2.Application/Reports/DTOs/UpdateReportDto.cs`
- `FY.WB.CSHero2.Application/Reports/DTOs/ReportDataDto.cs`
- `FY.WB.CSHero2.Application/Reports/DTOs/ReportSectionDto.cs`
- `FY.WB.CSHero2.Application/Reports/DTOs/ReportSectionFieldDto.cs`
- `FY.WB.CSHero2.Application/Reports/DTOs/ComponentDefinitionDto.cs`
- `FY.WB.CSHero2.Application/Reports/DTOs/RenderResultDto.cs`

### **Phase 3.5: Data Migration Service** (Days 4-5)

#### Task 3.5.1: Migration Strategy
```mermaid
flowchart TD
    A[Existing SQL Reports] --> B[Extract JSON Data]
    B --> C[Parse Sections & Fields]
    C --> D[Create Cosmos Documents]
    
    A --> E[Extract Component Definitions]
    E --> F[Generate Component Files]
    F --> G[Store in Azure Blob Storage]
    
    D --> H[Update SQL References]
    G --> H
    H --> I[Verify Migration]
    I --> J[Generate Migration Report]
```

#### Task 3.5.2: Implement Migration Service
```csharp
public interface IReportDataMigrationService
{
    Task<MigrationResult> MigrateReportDataAsync(CancellationToken cancellationToken = default);
    Task<MigrationResult> MigrateSpecificReportAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task<ValidationResult> ValidateMigrationAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task<MigrationStatus> GetMigrationStatusAsync(CancellationToken cancellationToken = default);
    Task<bool> RollbackMigrationAsync(Guid reportId, CancellationToken cancellationToken = default);
}

public class MigrationResult
{
    public int TotalReports { get; set; }
    public int SuccessfulMigrations { get; set; }
    public int FailedMigrations { get; set; }
    public List<string> Errors { get; set; } = new();
    public TimeSpan Duration { get; set; }
    public DateTime CompletedAt { get; set; }
}
```

**Files to Create:**
- `FY.WB.CSHero2.Application/Common/Interfaces/IReportDataMigrationService.cs`
- `FY.WB.CSHero2.Application/Reports/Services/ReportDataMigrationService.cs`
- `FY.WB.CSHero2.Application/Reports/DTOs/MigrationResult.cs`
- `FY.WB.CSHero2.Application/Reports/DTOs/ValidationResult.cs`

### **Phase 3.6: Dependency Injection and Configuration** (Day 5)

#### Task 3.6.1: Update DI Configuration
```csharp
// Update FY.WB.CSHero2.Infrastructure/DependencyInjection.cs
public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
{
    // Cosmos DB
    services.AddSingleton(sp => 
    {
        var connectionString = configuration["CosmosDb:ConnectionString"];
        return new CosmosClient(connectionString, new CosmosClientOptions
        {
            SerializerOptions = new CosmosSerializationOptions
            {
                PropertyNamingPolicy = CosmosPropertyNamingPolicy.CamelCase
            }
        });
    });

    // Azure Blob Storage
    services.AddSingleton(sp => 
    {
        var connectionString = configuration["BlobStorage:ConnectionString"];
        return new BlobServiceClient(connectionString);
    });

    // Repositories
    services.AddScoped<IReportDataRepository, ReportDataRepository>();
    services.AddScoped<IReportComponentsRepository, ReportComponentsRepository>();

    // Services
    services.AddScoped<IReportService, ReportService>();
    services.AddScoped<IReportDataService, ReportDataService>();
    services.AddScoped<IReportRenderingService, ReportRenderingService>();
    services.AddScoped<IReportDataMigrationService, ReportDataMigrationService>();

    return services;
}
```

### **Phase 3.7: Integration and Testing** (Day 6)

#### Task 3.7.1: Create Integration Tests
**Files to Create:**
- `FY.WB.CSHero2.Test/Integration/MultiStorageIntegrationTests.cs`
- `FY.WB.CSHero2.Test/Integration/ReportDataRepositoryTests.cs`
- `FY.WB.CSHero2.Test/Integration/ReportComponentsRepositoryTests.cs`
- `FY.WB.CSHero2.Test/Integration/ReportServiceTests.cs`
- `FY.WB.CSHero2.Test/Integration/MigrationServiceTests.cs`

#### Task 3.7.2: Update API Controllers
**Files to Update:**
- `FY.WB.CSHero2/Controllers/ReportsController.cs`
- Create `FY.WB.CSHero2/Controllers/ReportDataController.cs`
- Create `FY.WB.CSHero2/Controllers/ReportRenderingController.cs`
- Create `FY.WB.CSHero2/Controllers/MigrationController.cs`

## Risk Mitigation Strategies

### **Data Consistency Risks**
- **Mitigation**: Implement distributed transaction patterns with compensation actions
- **Fallback**: Rollback mechanisms for failed multi-storage operations
- **Monitoring**: Health checks across all storage types with alerting

### **Performance Risks**
- **Mitigation**: Implement caching strategies for frequently accessed data
- **Optimization**: Batch operations where possible, async/await patterns
- **Monitoring**: Performance metrics and response time alerting

### **Migration Risks**
- **Mitigation**: Comprehensive backup before migration, incremental migration approach
- **Validation**: Data integrity checks at each step with detailed logging
- **Rollback**: Ability to revert to previous state with minimal downtime

### **Azure Resource Costs**
- **Mitigation**: Start with minimal throughput settings, implement auto-scaling
- **Monitoring**: Cost alerts and usage monitoring
- **Optimization**: Lifecycle policies for blob storage, appropriate Cosmos DB RU allocation

## Success Criteria

1. ✅ **Functional**: All CRUD operations work seamlessly across SQL, Cosmos DB, and Blob Storage
2. ✅ **Performance**: Response times under 2 seconds for typical operations
3. ✅ **Data Integrity**: Zero data loss during migration with full audit trail
4. ✅ **Scalability**: Architecture supports 10x current data volume
5. ✅ **Maintainability**: Clean separation of concerns with comprehensive logging
6. ✅ **Cost Efficiency**: Azure resource costs within acceptable limits

## Implementation Timeline

| Phase | Duration | Dependencies | Deliverables |
|-------|----------|--------------|--------------|
| 3.1 | 1 day | None | Azure resources created, configuration updated |
| 3.2 | 1-2 days | 3.1 | Cosmos DB models and repository implemented |
| 3.3 | 1-2 days | 3.1 | Blob Storage models and repository implemented |
| 3.4 | 2 days | 3.2, 3.3 | Orchestration services implemented |
| 3.5 | 1-2 days | 3.4 | Migration service implemented |
| 3.6 | 1 day | 3.5 | DI configuration and API updates |
| 3.7 | 1 day | 3.6 | Integration tests and validation |

**Total Estimated Duration: 7-10 days**

## Next Steps

1. **Immediate**: Create Azure Blob Storage account and update configuration
2. **Phase 3.2**: Implement Cosmos DB models and repository
3. **Phase 3.3**: Implement Blob Storage models and repository
4. **Phase 3.4**: Implement orchestration services
5. **Phase 3.5**: Implement and test data migration
6. **Phase 3.6**: Complete integration and testing

This plan provides a comprehensive roadmap for implementing the multi-storage architecture while maintaining data integrity and system reliability throughout the process.