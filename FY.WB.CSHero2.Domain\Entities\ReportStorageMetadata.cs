using System;
using FY.WB.CSHero2.Domain.Entities.Core;

namespace FY.WB.CSHero2.Domain.Entities
{
    /// <summary>
    /// Tracks storage metadata and migration status for reports across multiple storage systems
    /// Enables cost optimization and migration monitoring
    /// </summary>
    public class ReportStorageMetadata : AuditedEntity<Guid>
    {
        /// <summary>
        /// Foreign key to the parent report
        /// </summary>
        public Guid ReportId { get; set; }
        
        /// <summary>
        /// Current storage strategy for this report
        /// Values: "SQL", "Hybrid", "MultiStorage"
        /// </summary>
        public string StorageStrategy { get; set; } = "SQL";
        
        /// <summary>
        /// Migration status tracking
        /// Values: "NotMigrated", "InProgress", "Completed", "Failed"
        /// </summary>
        public string MigrationStatus { get; set; } = "NotMigrated";
        
        /// <summary>
        /// When migration was started
        /// </summary>
        public DateTime? MigrationStartDate { get; set; }
        
        /// <summary>
        /// When migration was completed
        /// </summary>
        public DateTime? MigrationCompletedDate { get; set; }
        
        /// <summary>
        /// Error message if migration failed
        /// </summary>
        public string? MigrationErrorMessage { get; set; }
        
        /// <summary>
        /// Storage size in SQL Server (bytes)
        /// </summary>
        public long SqlStorageSize { get; set; }
        
        /// <summary>
        /// Storage size in Cosmos DB (bytes)
        /// </summary>
        public long CosmosStorageSize { get; set; }
        
        /// <summary>
        /// Storage size in Blob Storage (bytes)
        /// </summary>
        public long BlobStorageSize { get; set; }
        
        /// <summary>
        /// Total storage size across all systems (bytes)
        /// </summary>
        public long TotalStorageSize { get; set; }
        
        /// <summary>
        /// Number of times this report has been accessed
        /// </summary>
        public long AccessCount { get; set; }
        
        /// <summary>
        /// Last time this report was accessed
        /// </summary>
        public DateTime? LastAccessDate { get; set; }
        
        /// <summary>
        /// Performance metrics for storage operations (JSON)
        /// </summary>
        public string? PerformanceMetrics { get; set; }
        
        /// <summary>
        /// Additional metadata for storage optimization (JSON)
        /// </summary>
        public string? OptimizationMetadata { get; set; }
        
        // Navigation properties
        /// <summary>
        /// Navigation property to the parent report
        /// </summary>
        public virtual Report Report { get; set; } = null!;
        
        /// <summary>
        /// Default constructor for EF Core
        /// </summary>
        public ReportStorageMetadata() : base() { }
        
        /// <summary>
        /// Constructor with parameters
        /// </summary>
        public ReportStorageMetadata(
            Guid id,
            Guid reportId,
            string storageStrategy = "SQL",
            string migrationStatus = "NotMigrated")
            : base(id)
        {
            ReportId = reportId;
            StorageStrategy = storageStrategy;
            MigrationStatus = migrationStatus;
            SqlStorageSize = 0;
            CosmosStorageSize = 0;
            BlobStorageSize = 0;
            TotalStorageSize = 0;
            AccessCount = 0;
        }
        
        /// <summary>
        /// Updates storage sizes and recalculates total
        /// </summary>
        public void UpdateStorageSizes(long sqlSize, long cosmosSize, long blobSize)
        {
            SqlStorageSize = sqlSize;
            CosmosStorageSize = cosmosSize;
            BlobStorageSize = blobSize;
            TotalStorageSize = sqlSize + cosmosSize + blobSize;
        }
        
        /// <summary>
        /// Records an access to this report
        /// </summary>
        public void RecordAccess()
        {
            AccessCount++;
            LastAccessDate = DateTime.UtcNow;
        }
        
        /// <summary>
        /// Updates migration status
        /// </summary>
        public void UpdateMigrationStatus(string status, string? errorMessage = null)
        {
            MigrationStatus = status;
            
            switch (status)
            {
                case "InProgress":
                    MigrationStartDate = DateTime.UtcNow;
                    MigrationErrorMessage = null;
                    break;
                case "Completed":
                    MigrationCompletedDate = DateTime.UtcNow;
                    MigrationErrorMessage = null;
                    break;
                case "Failed":
                    MigrationErrorMessage = errorMessage;
                    break;
            }
        }
    }
}
