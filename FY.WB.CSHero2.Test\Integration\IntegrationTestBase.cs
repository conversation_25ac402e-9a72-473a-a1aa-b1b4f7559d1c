using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using FY.WB.CSHero2.Infrastructure;
using FY.WB.CSHero2.Infrastructure.Persistence;
using System;
using System.IO;
using System.Linq;

namespace FY.WB.CSHero2.Test.Integration
{
    /// <summary>
    /// Base class for integration tests that need access to the full DI container
    /// </summary>
    public abstract class IntegrationTestBase : IDisposable
    {
        protected readonly IServiceProvider ServiceProvider;
        protected readonly IConfiguration Configuration;
        private readonly IHost _host;

        protected IntegrationTestBase()
        {
            // Build configuration with test-specific settings
            // Find the project root by looking for the .sln file
            var currentDir = Directory.GetCurrentDirectory();
            var solutionDir = FindProjectRoot(currentDir);
            var projectRoot = Path.GetDirectoryName(solutionDir)!; // Go up one level from the .sln location
            var testProjectPath = Path.Combine(projectRoot, "FY.WB.CSHero2.Test");
            var mainProjectPath = Path.Combine(projectRoot, "FY.WB.CSHero2");
            
            Configuration = new ConfigurationBuilder()
                .SetBasePath(projectRoot)
                .AddJsonFile(Path.Combine(testProjectPath, "appsettings.Test.json"), optional: false)
                .AddJsonFile(Path.Combine(mainProjectPath, "appsettings.json"), optional: true)
                .AddInMemoryCollection(new Dictionary<string, string?>
                {
                    // Override with test-safe connection strings
                    ["ConnectionStrings:DefaultConnection"] = "Data Source=:memory:",
                    ["BlobStorage:ConnectionString"] = "UseDevelopmentStorage=true",
                    ["BlobStorage:ContainerName"] = "test-container"
                    // Note: CosmosDb configuration is now loaded from appsettings.Test.json
                })
                .AddEnvironmentVariables()
                .Build();

            // Build host with infrastructure services only
            _host = Host.CreateDefaultBuilder()
                .ConfigureServices((context, services) =>
                {
                    // Add required services for testing
                    services.AddHttpContextAccessor();
                    
                    // Add infrastructure services
                    services.AddInfrastructure(Configuration);
                    
                    // Override database context to use in-memory database for testing
                    var descriptor = services.FirstOrDefault(s => s.ServiceType == typeof(DbContextOptions<ApplicationDbContext>));
                    if (descriptor != null)
                    {
                        services.Remove(descriptor);
                    }
                    
                    services.AddDbContext<ApplicationDbContext>(options =>
                    {
                        options.UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}");
                        options.EnableSensitiveDataLogging();
                        options.EnableDetailedErrors();
                    });
                    
                    // Add logging
                    services.AddLogging(builder =>
                    {
                        builder.AddConsole();
                        builder.SetMinimumLevel(LogLevel.Information);
                    });
                })
                .Build();

            ServiceProvider = _host.Services;
        }

        protected async Task<ApplicationDbContext> GetDbContextAsync()
        {
            using var scope = ServiceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            await context.Database.EnsureCreatedAsync();
            return context;
        }

        private static string FindProjectRoot(string startDirectory)
        {
            var directory = new DirectoryInfo(startDirectory);
            while (directory != null)
            {
                // Look for the solution file - this indicates the main project root
                if (directory.GetFiles("*.sln").Any())
                {
                    // The solution is in the main project directory (FY.WB.CSHero2)
                    // We want to return this directory, not its parent
                    return directory.FullName;
                }
                
                // Also check subdirectories for the solution file
                var subDirs = directory.GetDirectories();
                foreach (var subDir in subDirs)
                {
                    if (subDir.GetFiles("*.sln").Any())
                    {
                        return subDir.FullName;
                    }
                }
                
                directory = directory.Parent;
            }
            
            throw new InvalidOperationException($"Could not find project root with .sln file starting from {startDirectory}");
        }

        public void Dispose()
        {
            _host?.Dispose();
        }
    }
}
