using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using FY.WB.CSHero2.Domain.Entities;

namespace FY.WB.CSHero2.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for ReportVersion entity
    /// </summary>
    public class ReportVersionConfiguration : IEntityTypeConfiguration<ReportVersion>
    {
        public void Configure(EntityTypeBuilder<ReportVersion> builder)
        {
            // Table configuration
            builder.ToTable("ReportVersions");

            // Primary key
            builder.HasKey(rv => rv.Id);

            // Properties
            builder.Property(rv => rv.Id)
                .IsRequired()
                .ValueGeneratedOnAdd();

            builder.Property(rv => rv.ReportId)
                .IsRequired();

            builder.Property(rv => rv.VersionNumber)
                .IsRequired();

            builder.Property(rv => rv.Description)
                .HasMaxLength(500)
                .IsRequired();

            // CreatedAt and CreatedBy are handled by the base AuditedEntity configuration

            builder.Property(rv => rv.ComponentDataJson)
                .HasColumnType("nvarchar(max)")
                .IsRequired();

            builder.Property(rv => rv.JsonData)
                .HasColumnType("nvarchar(max)")
                .IsRequired();

            builder.Property(rv => rv.IsCurrent)
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(rv => rv.ComponentDataSize)
                .IsRequired()
                .HasDefaultValue(0);

            builder.Property(rv => rv.JsonDataSize)
                .IsRequired()
                .HasDefaultValue(0);

            // V3 Multi-storage Properties
            builder.Property(rv => rv.DataDocumentId)
                .IsRequired(false)
                .HasMaxLength(100);

            builder.Property(rv => rv.ComponentsBlobId)
                .IsRequired(false)
                .HasMaxLength(255);

            // Indexes
            builder.HasIndex(rv => new { rv.ReportId, rv.VersionNumber })
                .IsUnique()
                .HasDatabaseName("IX_ReportVersions_ReportId_VersionNumber");

            builder.HasIndex(rv => rv.ReportId)
                .HasDatabaseName("IX_ReportVersions_ReportId");

            builder.HasIndex(rv => rv.IsCurrent)
                .HasDatabaseName("IX_ReportVersions_IsCurrent");

            builder.HasIndex(rv => rv.CreationTime)
                .HasDatabaseName("IX_ReportVersions_CreationTime");

            // Relationships
            builder.HasOne(rv => rv.Report)
                .WithMany(r => r.Versions)
                .HasForeignKey(rv => rv.ReportId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasMany(rv => rv.ComponentDefinitions)
                .WithOne(cd => cd.ReportVersion)
                .HasForeignKey(cd => cd.ReportVersionId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
