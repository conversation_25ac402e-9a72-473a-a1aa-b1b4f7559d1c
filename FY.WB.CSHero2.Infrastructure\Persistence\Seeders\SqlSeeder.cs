using FY.WB.CSHero2.Domain.Entities;
using FY.WB.CSHero2.Domain.Entities.Core;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Runtime.CompilerServices;
using System.Text.Json;

namespace FY.WB.CSHero2.Infrastructure.Persistence.Seeders
{
    public interface ISqlSeeder
    {
        Task SeedAsync(ApplicationDbContext context, CancellationToken cancellationToken = default);
        bool IsTableEmpty<T>(ApplicationDbContext context) where T : class;
    }

    public class SqlSeeder : ISqlSeeder
    {
        private readonly ILogger<SqlSeeder> _logger;
        private static readonly JsonSerializerOptions JsonOptions = new()
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        };

        public SqlSeeder(ILogger<SqlSeeder> logger)
        {
            _logger = logger;
        }

        public async Task SeedAsync(ApplicationDbContext context, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Starting SQL Server seeding...");

            try
            {
                // Check table status
                var tableStatus = await GetTableStatusAsync(context);
                LogTableStatus(tableStatus);

                // Seed in correct dependency order to avoid FK constraint violations
                // Phase 1: Independent entities (no foreign key dependencies)
                await SeedTenantProfilesAsync(context, tableStatus.TenantProfilesEmpty, cancellationToken);
                await SeedTemplatesAsync(context, tableStatus.TemplatesEmpty, cancellationToken);
                
                // Phase 2: Entities that depend on TenantProfiles
                await SeedClientsAsync(context, tableStatus.ClientsEmpty, cancellationToken);
                
                // Phase 3: Reports (depend on Clients and TenantProfiles)
                await SeedReportsAsync(context, tableStatus.ReportsEmpty, cancellationToken);
                
                // Phase 4: Entities that depend on Reports
                await SeedReportVersionsAsync(context, tableStatus.ReportVersionsEmpty, cancellationToken);
                await SeedReportStorageMetadataAsync(context, tableStatus.ReportStorageMetadataEmpty, cancellationToken);
                await SeedReportStylesAsync(context, tableStatus.ReportStylesEmpty, cancellationToken);
                await SeedReportSectionsAsync(context, tableStatus.ReportSectionsEmpty, cancellationToken);

                // Phase 5: Entities that depend on ReportSections
                await SeedReportSectionFieldsAsync(context, tableStatus.ReportSectionFieldsEmpty, cancellationToken);
                
                // Note: Deprecated entities (Forms, Invoices, Uploads) are no longer seeded
                // Tables remain in schema for application use, but no seed data is provided

                _logger.LogInformation("SQL Server seeding completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during SQL Server seeding");
                throw;
            }
        }

        public bool IsTableEmpty<T>(ApplicationDbContext context) where T : class
        {
            return !context.Set<T>().IgnoreQueryFilters().Any();
        }

        private async Task<TableStatus> GetTableStatusAsync(ApplicationDbContext context)
        {
            return new TableStatus
            {
                TenantProfilesEmpty = IsTableEmpty<TenantProfile>(context),
                ClientsEmpty = IsTableEmpty<Client>(context),
                ReportsEmpty = IsTableEmpty<Report>(context),
                ReportVersionsEmpty = IsTableEmpty<ReportVersion>(context),
                ReportStorageMetadataEmpty = IsTableEmpty<ReportStorageMetadata>(context),
                ReportStylesEmpty = IsTableEmpty<ReportStyle>(context),
                ReportSectionsEmpty = IsTableEmpty<ReportSection>(context),
                ReportSectionFieldsEmpty = IsTableEmpty<ReportSectionField>(context),
                TemplatesEmpty = IsTableEmpty<Template>(context)
            };
        }

        private void LogTableStatus(TableStatus status)
        {
            _logger.LogInformation("SQL Table Status: " +
                "TenantProfiles={TenantProfiles}, Clients={Clients}, Reports={Reports}, " +
                "ReportVersions={ReportVersions}, ReportStorageMetadata={ReportStorageMetadata}, " +
                "ReportStyles={ReportStyles}, ReportSections={ReportSections}, " +
                "ReportSectionFields={ReportSectionFields}, Templates={Templates}",
                status.TenantProfilesEmpty ? "Empty" : "Has Data",
                status.ClientsEmpty ? "Empty" : "Has Data",
                status.ReportsEmpty ? "Empty" : "Has Data",
                status.ReportVersionsEmpty ? "Empty" : "Has Data",
                status.ReportStorageMetadataEmpty ? "Empty" : "Has Data",
                status.ReportStylesEmpty ? "Empty" : "Has Data",
                status.ReportSectionsEmpty ? "Empty" : "Has Data",
                status.ReportSectionFieldsEmpty ? "Empty" : "Has Data",
                status.TemplatesEmpty ? "Empty" : "Has Data");
        }

        private async Task SeedTenantProfilesAsync(ApplicationDbContext context, bool isEmpty, CancellationToken cancellationToken)
        {
            if (!isEmpty)
            {
                _logger.LogInformation("Skipping TenantProfiles seeding - table has data");
                return;
            }

            _logger.LogInformation("Seeding TenantProfiles...");
            var dtos = await ReadJsonData<TenantProfileSeedDto>("tenant-profiles.json");
            var entities = new List<TenantProfile>();

            foreach (var dto in dtos)
            {
                var entity = new TenantProfile(
                    dto.Id, dto.Name, dto.Email, dto.Status, dto.Phone,
                    dto.Company, dto.Subscription, dto.LastLoginTime,
                    dto.BillingCycle, dto.NextBillingDate, dto.SubscriptionStatus);

                entity.PaymentMethod = dto.PaymentMethod != null 
                    ? JsonSerializer.Serialize(dto.PaymentMethod, JsonOptions) 
                    : "{}";
                entity.BillingAddress = dto.BillingAddress != null 
                    ? JsonSerializer.Serialize(dto.BillingAddress, JsonOptions) 
                    : "{}";
                entity.TenantId = dto.Id;
                entity.CreationTime = DateTime.UtcNow;
                entity.LastModificationTime = DateTime.UtcNow;

                entities.Add(entity);
            }

            await SeedEntities(context, entities, "TenantProfile", cancellationToken);
        }

        private async Task SeedClientsAsync(ApplicationDbContext context, bool isEmpty, CancellationToken cancellationToken)
        {
            if (!isEmpty)
            {
                _logger.LogInformation("Skipping Clients seeding - table has data");
                return;
            }

            _logger.LogInformation("Seeding Clients...");
            var entities = await ReadJsonData<Client>("clients.json");
            await SeedEntities(context, entities, "Client", cancellationToken);
        }

        private async Task SeedReportsAsync(ApplicationDbContext context, bool isEmpty, CancellationToken cancellationToken)
        {
            if (!isEmpty)
            {
                _logger.LogInformation("Skipping Reports seeding - table has data");
                return;
            }

            _logger.LogInformation("Seeding Reports with FK validation...");
            var reportDtos = await ReadJsonData<Report>("reports.json");
            var allClients = await context.Clients.IgnoreQueryFilters().ToListAsync(cancellationToken);
            var validReports = new List<Report>();
            var skippedCount = 0;

            foreach (var reportDto in reportDtos)
            {
                var client = allClients.FirstOrDefault(c => 
                    c.CompanyName == reportDto.ClientName && 
                    c.TenantId == reportDto.TenantId);

                if (client == null)
                {
                    _logger.LogWarning("Skipping Report '{ReportName}' - Client '{ClientName}' not found for TenantId {TenantId}",
                        reportDto.Name, reportDto.ClientName, reportDto.TenantId);
                    skippedCount++;
                    continue;
                }

                var report = new Report(
                    reportDto.Id, reportDto.ReportNumber, client.Id, reportDto.ClientName,
                    reportDto.Name, reportDto.Category, reportDto.SlideCount, reportDto.Status, reportDto.Author);

                report.TenantId = reportDto.TenantId;
                report.CreationTime = reportDto.CreationTime == default ? DateTime.UtcNow : reportDto.CreationTime;
                report.LastModificationTime = reportDto.LastModificationTime ?? DateTime.UtcNow;
                report.IsDeleted = reportDto.IsDeleted;

                // Set new multi-storage properties
                report.IsDraft = false; // Seed data represents saved reports
                report.LastSavedAt = report.CreationTime;

                validReports.Add(report);
            }

            await SeedEntities(context, validReports, "Report", cancellationToken);
            if (skippedCount > 0)
            {
                _logger.LogWarning("Skipped {SkippedCount} reports due to missing client references", skippedCount);
            }
        }

        private async Task SeedReportVersionsAsync(ApplicationDbContext context, bool isEmpty, CancellationToken cancellationToken)
        {
            if (!isEmpty)
            {
                _logger.LogInformation("Skipping ReportVersions seeding - table has data");
                return;
            }

            _logger.LogInformation("Seeding ReportVersions with FK validation...");
            var versionDtos = await ReadJsonData<ReportVersion>("report-versions.json");
            var allReports = await context.Reports.IgnoreQueryFilters().ToListAsync(cancellationToken);
            var validVersions = new List<ReportVersion>();
            var skippedCount = 0;

            foreach (var versionDto in versionDtos)
            {
                var reportExists = allReports.Any(r => r.Id == versionDto.ReportId);

                if (!reportExists)
                {
                    _logger.LogWarning("Skipping ReportVersion - Report not found (SqlSeeder.cs:225)");
                    skippedCount++;
                    continue;
                }

                // Set new multi-storage properties
                versionDto.StorageStrategy = "SQL"; // Start with SQL, upgrade during migration
                versionDto.DataDocumentId = null; // Will be set by CosmosSeeder
                versionDto.ComponentsBlobId = null; // Will be set by BlobSeeder
                versionDto.StylesBlobId = null; // Will be set by BlobSeeder

                validVersions.Add(versionDto);
            }

            await SeedEntities(context, validVersions, "ReportVersion", cancellationToken);
            if (skippedCount > 0)
            {
                _logger.LogWarning("Skipped {SkippedCount} report versions due to missing report references", skippedCount);
            }
        }

        private async Task SeedReportStylesAsync(ApplicationDbContext context, bool isEmpty, CancellationToken cancellationToken)
        {
            if (!isEmpty)
            {
                _logger.LogInformation("Skipping ReportStyles seeding - table has data");
                return;
            }

            _logger.LogInformation("Seeding ReportStyles...");
            var entities = await ReadJsonData<ReportStyle>("report-styles.json");
            await SeedEntities(context, entities, "ReportStyle", cancellationToken);
        }

        private async Task SeedReportSectionsAsync(ApplicationDbContext context, bool isEmpty, CancellationToken cancellationToken)
        {
            if (!isEmpty)
            {
                _logger.LogInformation("Skipping ReportSections seeding - table has data");
                return;
            }

            _logger.LogInformation("Seeding ReportSections with FK validation...");
            var sectionDtos = await ReadJsonData<ReportSection>("report-sections.json");
            var allReports = await context.Reports.IgnoreQueryFilters().ToListAsync(cancellationToken);
            var validSections = new List<ReportSection>();
            var skippedCount = 0;

            foreach (var sectionDto in sectionDtos)
            {
                var reportExists = allReports.Any(r => r.Id == sectionDto.ReportId);

                if (!reportExists)
                {
                    _logger.LogWarning("Skipping ReportSection '{SectionId}' - Report '{ReportId}' not found",
                        sectionDto.Id, sectionDto.ReportId);
                    skippedCount++;
                    continue;
                }

                validSections.Add(sectionDto);
            }

            await SeedEntities(context, validSections, "ReportSection", cancellationToken);
            if (skippedCount > 0)
            {
                _logger.LogWarning("Skipped {SkippedCount} report sections due to missing report references", skippedCount);
            }
        }

        private async Task SeedReportSectionFieldsAsync(ApplicationDbContext context, bool isEmpty, CancellationToken cancellationToken)
        {
            if (!isEmpty)
            {
                _logger.LogInformation("Skipping ReportSectionFields seeding - table has data");
                return;
            }

            _logger.LogInformation("Seeding ReportSectionFields...");
            var entities = await ReadJsonData<ReportSectionField>("report-section-fields.json");
            await SeedEntities(context, entities, "ReportSectionField", cancellationToken);
        }


        private async Task SeedTemplatesAsync(ApplicationDbContext context, bool isEmpty, CancellationToken cancellationToken)
        {
            if (!isEmpty)
            {
                _logger.LogInformation("Skipping Templates seeding - table has data");
                return;
            }

            _logger.LogInformation("Seeding Templates...");
            var dtos = await ReadJsonData<TemplateSeedDto>("templates.json");
            var entities = new List<Template>();

            foreach (var dto in dtos)
            {
                var entity = new Template(dto.Id, dto.Name, dto.Description, dto.Category, dto.ThumbnailUrl);
                entity.Tags = dto.Tags != null ? JsonSerializer.Serialize(dto.Tags, JsonOptions) : "[]";
                entity.Sections = dto.Sections != null ? JsonSerializer.Serialize(dto.Sections, JsonOptions) : "[]";
                entity.Fields = dto.Fields != null ? JsonSerializer.Serialize(dto.Fields, JsonOptions) : "[]";
                entity.TenantId = dto.TenantId;
                entity.CreationTime = DateTime.UtcNow;
                entity.LastModificationTime = DateTime.UtcNow;

                entities.Add(entity);
            }

            await SeedEntities(context, entities, "Template", cancellationToken);
        }


        private async Task<List<T>> ReadJsonData<T>(string fileName)
        {
            var path = FindSeedDataFile(fileName);
            if (string.IsNullOrEmpty(path))
            {
                _logger.LogWarning("Seed data file not found: {FileName}", fileName);
                return new List<T>();
            }

            try
            {
                _logger.LogDebug("Reading seed data from: {FilePath}", path);
                var json = await File.ReadAllTextAsync(path);
                var data = JsonSerializer.Deserialize<List<T>>(json, JsonOptions);
                if (data == null || !data.Any())
                {
                    _logger.LogWarning("No data found in seed file: {FileName}", fileName);
                    return new List<T>();
                }
                _logger.LogInformation("Successfully read {Count} records from {FileName}", data.Count, fileName);
                return data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading seed data from {FileName} at path {FilePath}", fileName, path);
                throw;
            }
        }

        private string? FindSeedDataFile(string fileName)
        {
            // Strategy 1: Use assembly location as base
            var assemblyLocation = System.Reflection.Assembly.GetExecutingAssembly().Location;
            var assemblyDir = Path.GetDirectoryName(assemblyLocation);
            
            if (!string.IsNullOrEmpty(assemblyDir))
            {
                // Look for SeedData folder relative to assembly location
                var seedDataPaths = new[]
                {
                    Path.Combine(assemblyDir, "Persistence", "SeedData", fileName),
                    Path.Combine(assemblyDir, "..", "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName),
                    Path.Combine(assemblyDir, "..", "..", "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName),
                    Path.Combine(assemblyDir, "..", "..", "..", "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName),
                    Path.Combine(assemblyDir, "..", "..", "..", "..", "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName)
                };

                foreach (var seedPath in seedDataPaths)
                {
                    var fullPath = Path.GetFullPath(seedPath);
                    _logger.LogDebug("Checking seed data path: {Path}", fullPath);
                    if (File.Exists(fullPath))
                    {
                        _logger.LogDebug("Found seed data file at: {Path}", fullPath);
                        return fullPath;
                    }
                }
            }

            // Strategy 2: Use current working directory
            var currentDir = Directory.GetCurrentDirectory();
            var workingDirPaths = new[]
            {
                Path.Combine(currentDir, "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName),
                Path.Combine(currentDir, "..", "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName),
                Path.Combine(currentDir, "..", "..", "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName)
            };

            foreach (var workingPath in workingDirPaths)
            {
                var fullPath = Path.GetFullPath(workingPath);
                _logger.LogDebug("Checking working directory path: {Path}", fullPath);
                if (File.Exists(fullPath))
                {
                    _logger.LogDebug("Found seed data file at: {Path}", fullPath);
                    return fullPath;
                }
            }

            // Strategy 3: Search from AppContext.BaseDirectory
            var baseDir = AppContext.BaseDirectory;
            var baseDirPaths = new[]
            {
                Path.Combine(baseDir, "Persistence", "SeedData", fileName),
                Path.Combine(baseDir, "..", "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName),
                Path.Combine(baseDir, "..", "..", "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName),
                Path.Combine(baseDir, "..", "..", "..", "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName),
                Path.Combine(baseDir, "..", "..", "..", "..", "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName)
            };

            foreach (var basePath in baseDirPaths)
            {
                var fullPath = Path.GetFullPath(basePath);
                _logger.LogDebug("Checking base directory path: {Path}", fullPath);
                if (File.Exists(fullPath))
                {
                    _logger.LogDebug("Found seed data file at: {Path}", fullPath);
                    return fullPath;
                }
            }

            _logger.LogError("Could not locate seed data file: {FileName}. Searched in assembly location: {AssemblyDir}, working directory: {WorkingDir}, base directory: {BaseDir}",
                fileName, assemblyDir, currentDir, baseDir);
            return null;
        }

        private async Task SeedEntities<T>(ApplicationDbContext context, List<T> entities, string entityName, CancellationToken cancellationToken) where T : class
        {
            var addedCount = 0;
            var skippedCount = 0;

            foreach (var entity in entities)
            {
                try
                {
                    // Check if entity already exists (assuming entities have Id property)
                    var idProperty = typeof(T).GetProperty("Id");
                    if (idProperty != null)
                    {
                        var id = idProperty.GetValue(entity);
                        var exists = await context.Set<T>().IgnoreQueryFilters()
                            .AnyAsync(e => EF.Property<object>(e, "Id").Equals(id), cancellationToken);

                        if (!exists)
                        {
                            // Set audit properties if entity supports them
                            SetAuditProperties(entity);
                            context.Set<T>().Add(entity);
                            addedCount++;
                        }
                        else
                        {
                            skippedCount++;
                        }
                    }
                    else
                    {
                        // Fallback for entities without Id property
                        context.Set<T>().Add(entity);
                        addedCount++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing {EntityName} record", entityName);
                    throw;
                }
            }

            if (addedCount > 0)
            {
                await context.SaveChangesAsync(cancellationToken);
            }

            _logger.LogInformation("Completed seeding {EntityName}: {Added} added, {Skipped} skipped",
                entityName, addedCount, skippedCount);
        }

        private static void SetAuditProperties<T>(T entity)
        {
            var now = DateTime.UtcNow;

            // Set CreationTime if property exists and is default
            var creationTimeProperty = typeof(T).GetProperty("CreationTime");
            if (creationTimeProperty != null && creationTimeProperty.CanWrite)
            {
                var currentValue = creationTimeProperty.GetValue(entity);
                if (currentValue is DateTime dt && dt == default)
                {
                    creationTimeProperty.SetValue(entity, now);
                }
            }

            // Set LastModificationTime if property exists and is null
            var lastModificationTimeProperty = typeof(T).GetProperty("LastModificationTime");
            if (lastModificationTimeProperty != null && lastModificationTimeProperty.CanWrite)
            {
                var currentValue = lastModificationTimeProperty.GetValue(entity);
                if (currentValue == null)
                {
                    lastModificationTimeProperty.SetValue(entity, now);
                }
            }
        }

        // DTO classes for JSON deserialization
        public class TenantProfileSeedDto
        {
            public Guid Id { get; set; }
            public string Name { get; set; } = string.Empty;
            public string Email { get; set; } = string.Empty;
            public string Status { get; set; } = string.Empty;
            public string Phone { get; set; } = string.Empty;
            public string Company { get; set; } = string.Empty;
            public string Subscription { get; set; } = string.Empty;
            public DateTime LastLoginTime { get; set; }
            public string BillingCycle { get; set; } = string.Empty;
            public DateTime NextBillingDate { get; set; }
            public string SubscriptionStatus { get; set; } = string.Empty;
            public PaymentMethodInfo? PaymentMethod { get; set; }
            public BillingAddressInfo? BillingAddress { get; set; }
            public Guid? TenantId { get; set; }
        }

        public class TemplateSeedDto
        {
            public Guid Id { get; set; }
            public string Name { get; set; } = string.Empty;
            public string Description { get; set; } = string.Empty;
            public string Category { get; set; } = string.Empty;
            public string ThumbnailUrl { get; set; } = string.Empty;
            public List<string>? Tags { get; set; }
            public List<TemplateSection>? Sections { get; set; }
            public List<TemplateField>? Fields { get; set; }
            public Guid? TenantId { get; set; }
        }

        private class TableStatus
        {
            public bool TenantProfilesEmpty { get; set; }
            public bool ClientsEmpty { get; set; }
            public bool ReportsEmpty { get; set; }
            public bool ReportVersionsEmpty { get; set; }
            public bool ReportStorageMetadataEmpty { get; set; }
            public bool ReportStylesEmpty { get; set; }
            public bool ReportSectionsEmpty { get; set; }
            public bool ReportSectionFieldsEmpty { get; set; }
            public bool TemplatesEmpty { get; set; }
        }
    }
}
