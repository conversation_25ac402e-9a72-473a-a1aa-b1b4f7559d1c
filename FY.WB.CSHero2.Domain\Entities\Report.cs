using System;
using System.Collections.Generic;
using System.Linq;
using FY.WB.CSHero2.Domain.Entities.Core;

namespace FY.WB.CSHero2.Domain.Entities
{
    public class Report : FullAuditedMultiTenantEntity<Guid>
    {
        public string ReportNumber { get; set; } = string.Empty; // e.g., "CSR-2025-001"
        public Guid ClientId { get; set; } // Foreign key to Client
        public string ClientName { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty; // reportName in JSON
        public string Category { get; set; } = string.Empty;
        public int SlideCount { get; set; }
        public string Status { get; set; } = string.Empty;
        public string Author { get; set; } = string.Empty;

        /// <summary>
        /// Foreign key to the template this report was created from (V2 feature)
        /// </summary>
        public Guid? TemplateId { get; set; }

        /// <summary>
        /// Foreign key to the current active version of this report (V2 feature)
        /// </summary>
        public Guid? CurrentVersionId { get; set; }

        /// <summary>
        /// Report type for categorization (Standard, Custom, Template-based)
        /// </summary>
        public string ReportType { get; set; } = "Standard";

        /// <summary>
        /// Reference to Cosmos DB document containing report data (V3 multi-storage feature)
        /// </summary>
        public string? DataDocumentId { get; set; }

        /// <summary>
        /// Reference to Azure Blob Storage containing rendered components (V3 multi-storage feature)
        /// </summary>
        public string? ComponentsBlobId { get; set; }

        /// <summary>
        /// Whether this report is in draft state (not yet saved)
        /// </summary>
        public bool IsDraft { get; set; } = true;

        /// <summary>
        /// When this report was last saved (null if never saved)
        /// </summary>
        public DateTime? LastSavedAt { get; set; }

        /// <summary>
        /// Reference to Cosmos DB document containing draft data
        /// </summary>
        public string? DraftDataDocumentId { get; set; }

        // Navigation properties
        public virtual Client? Client { get; set; }

        /// <summary>
        /// Navigation property to the template this report was created from (V2 feature)
        /// </summary>
        public virtual Template? Template { get; set; }

        /// <summary>
        /// Navigation property to all versions of this report (V2 feature)
        /// </summary>
        public virtual ICollection<ReportVersion> Versions { get; set; } = new List<ReportVersion>();

        /// <summary>
        /// Navigation property to all sections of this report
        /// </summary>
        public virtual ICollection<ReportSection> Sections { get; set; } = new List<ReportSection>();

        public Report() : base() { }

        public Report(
            Guid id,
            string reportNumber,
            Guid clientId,
            string clientName,
            string name,
            string category,
            int slideCount,
            string status,
            string author)
            : base(id)
        {
            ReportNumber = reportNumber;
            ClientId = clientId;
            ClientName = clientName;
            Name = name;
            Category = category;
            SlideCount = slideCount;
            Status = status;
            Author = author;
        }

        public void UpdateDetails(
            string name,
            string category,
            int slideCount,
            string status,
            string author)
        {
            Name = name;
            Category = category;
            SlideCount = slideCount;
            Status = status;
            Author = author;
            // LastModificationTime and LastModifierId will be set by DbContext
        }

        public void SetStatus(string status)
        {
            Status = status;
            // LastModificationTime and LastModifierId will be set by DbContext
        }

        public void UpdateSlideCount(int count)
        {
            SlideCount = count;
            // LastModificationTime and LastModifierId will be set by DbContext
        }

        /// <summary>
        /// Gets the current version of this report (V2 feature)
        /// </summary>
        public ReportVersion? GetCurrentVersion()
        {
            return Versions.FirstOrDefault(v => v.IsCurrent);
        }

        /// <summary>
        /// Gets the latest version number (V2 feature)
        /// </summary>
        public int GetLatestVersionNumber()
        {
            return Versions.Any() ? Versions.Max(v => v.VersionNumber) : 0;
        }

        /// <summary>
        /// Sets a specific version as current (V2 feature)
        /// </summary>
        public void SetCurrentVersion(Guid versionId)
        {
            // Mark all versions as not current
            foreach (var version in Versions)
            {
                version.SetAsNotCurrent();
            }

            // Mark the specified version as current
            var targetVersion = Versions.FirstOrDefault(v => v.Id == versionId);
            if (targetVersion != null)
            {
                targetVersion.SetAsCurrent();
                CurrentVersionId = versionId;
            }
        }

        /// <summary>
        /// Checks if this report was created from a template (V2 feature)
        /// </summary>
        public bool IsTemplateBasedReport()
        {
            return TemplateId.HasValue;
        }

        /// <summary>
        /// Gets the total number of versions for this report (V2 feature)
        /// </summary>
        public int GetVersionCount()
        {
            return Versions.Count;
        }

        /// <summary>
        /// Checks if the report has any versions (V2 feature)
        /// </summary>
        public bool HasVersions()
        {
            return Versions.Any();
        }

        /// <summary>
        /// Gets versions ordered by version number (V2 feature)
        /// </summary>
        public IEnumerable<ReportVersion> GetVersionsOrdered()
        {
            return Versions.OrderBy(v => v.VersionNumber);
        }

        /// <summary>
        /// Sets the template for this report (V2 feature)
        /// </summary>
        public void SetTemplate(Guid templateId)
        {
            TemplateId = templateId;
            ReportType = "Template-based";
        }
    }
}
