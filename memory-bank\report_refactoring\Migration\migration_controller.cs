using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MediatR;
using FY.WB.CSHero2.Application.Commands.Migration;
using FY.WB.CSHero2.Application.Models.Migration;
using System.Security.Claims;

namespace FY.WB.CSHero2.Controllers
{
    /// <summary>
    /// Controller for managing report data migration operations
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "Admin,SuperAdmin")]
    public class MigrationController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<MigrationController> _logger;

        public MigrationController(IMediator mediator, ILogger<MigrationController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        /// <summary>
        /// Gets the current user ID from claims
        /// </summary>
        private Guid? GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            return Guid.TryParse(userIdClaim, out var userId) ? userId : null;
        }

        /// <summary>
        /// Starts migration of all reports from SQL to multi-storage architecture
        /// </summary>
        /// <param name="options">Migration options and configuration</param>
        /// <param name="correlationId">Optional correlation ID for tracking</param>
        /// <returns>Migration result with statistics and status</returns>
        [HttpPost("start")]
        [ProducesResponseType(typeof(MigrationResult), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(403)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<MigrationResult>> StartMigrationAsync(
            [FromBody] MigrationOptions? options = null,
            [FromHeader(Name = "X-Correlation-ID")] string? correlationId = null)
        {
            try
            {
                _logger.LogInformation("Starting full migration operation. User: {UserId}, CorrelationId: {CorrelationId}", 
                    GetCurrentUserId(), correlationId);

                var command = new MigrateAllReportsCommand
                {
                    Options = options ?? new MigrationOptions(),
                    InitiatedBy = GetCurrentUserId(),
                    CorrelationId = correlationId
                };

                var result = await _mediator.Send(command);

                _logger.LogInformation("Migration operation completed. Success: {Success}, Reports: {TotalReports}, Duration: {Duration}", 
                    result.Success, result.TotalReports, result.Duration);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting migration operation");
                return StatusCode(500, new { error = "An error occurred while starting the migration", details = ex.Message });
            }
        }

        /// <summary>
        /// Migrates a specific report and all its versions
        /// </summary>
        /// <param name="reportId">Report ID to migrate</param>
        /// <param name="options">Migration options and configuration</param>
        /// <param name="correlationId">Optional correlation ID for tracking</param>
        /// <returns>Migration result for the specific report</returns>
        [HttpPost("report/{reportId:guid}")]
        [ProducesResponseType(typeof(MigrationResult), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(403)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<MigrationResult>> MigrateReportAsync(
            Guid reportId,
            [FromBody] MigrationOptions? options = null,
            [FromHeader(Name = "X-Correlation-ID")] string? correlationId = null)
        {
            try
            {
                _logger.LogInformation("Starting migration for report {ReportId}. User: {UserId}", 
                    reportId, GetCurrentUserId());

                var command = new MigrateReportCommand
                {
                    ReportId = reportId,
                    Options = options ?? new MigrationOptions(),
                    InitiatedBy = GetCurrentUserId(),
                    CorrelationId = correlationId
                };

                var result = await _mediator.Send(command);

                _logger.LogInformation("Report migration completed. ReportId: {ReportId}, Success: {Success}", 
                    reportId, result.Success);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error migrating report {ReportId}", reportId);
                return StatusCode(500, new { error = "An error occurred while migrating the report", details = ex.Message });
            }
        }

        /// <summary>
        /// Migrates a specific report version
        /// </summary>
        /// <param name="reportId">Report ID</param>
        /// <param name="versionId">Version ID to migrate</param>
        /// <param name="options">Migration options and configuration</param>
        /// <param name="correlationId">Optional correlation ID for tracking</param>
        /// <returns>Migration result for the specific version</returns>
        [HttpPost("report/{reportId:guid}/version/{versionId:guid}")]
        [ProducesResponseType(typeof(MigrationResult), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(403)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<MigrationResult>> MigrateReportVersionAsync(
            Guid reportId,
            Guid versionId,
            [FromBody] MigrationOptions? options = null,
            [FromHeader(Name = "X-Correlation-ID")] string? correlationId = null)
        {
            try
            {
                _logger.LogInformation("Starting migration for report {ReportId}, version {VersionId}. User: {UserId}", 
                    reportId, versionId, GetCurrentUserId());

                var command = new MigrateReportVersionCommand
                {
                    ReportId = reportId,
                    VersionId = versionId,
                    Options = options ?? new MigrationOptions(),
                    InitiatedBy = GetCurrentUserId(),
                    CorrelationId = correlationId
                };

                var result = await _mediator.Send(command);

                _logger.LogInformation("Version migration completed. ReportId: {ReportId}, VersionId: {VersionId}, Success: {Success}", 
                    reportId, versionId, result.Success);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error migrating report {ReportId}, version {VersionId}", reportId, versionId);
                return StatusCode(500, new { error = "An error occurred while migrating the report version", details = ex.Message });
            }
        }

        /// <summary>
        /// Gets the current migration status and progress
        /// </summary>
        /// <param name="includeDetails">Whether to include detailed progress information</param>
        /// <param name="includeHistory">Whether to include recent operations history</param>
        /// <returns>Current migration status</returns>
        [HttpGet("status")]
        [ProducesResponseType(typeof(MigrationStatus), 200)]
        [ProducesResponseType(401)]
        [ProducesResponseType(403)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<MigrationStatus>> GetMigrationStatusAsync(
            [FromQuery] bool includeDetails = true,
            [FromQuery] bool includeHistory = true)
        {
            try
            {
                var query = new GetMigrationStatusQuery
                {
                    IncludeDetails = includeDetails,
                    IncludeHistory = includeHistory
                };

                var result = await _mediator.Send(query);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting migration status");
                return StatusCode(500, new { error = "An error occurred while getting migration status", details = ex.Message });
            }
        }

        /// <summary>
        /// Gets detailed migration statistics
        /// </summary>
        /// <param name="tenantId">Optional tenant ID filter</param>
        /// <param name="startDate">Date range start for statistics</param>
        /// <param name="endDate">Date range end for statistics</param>
        /// <returns>Migration statistics and metrics</returns>
        [HttpGet("statistics")]
        [ProducesResponseType(typeof(MigrationStatistics), 200)]
        [ProducesResponseType(401)]
        [ProducesResponseType(403)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<MigrationStatistics>> GetMigrationStatisticsAsync(
            [FromQuery] Guid? tenantId = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                var query = new GetMigrationStatisticsQuery
                {
                    TenantId = tenantId,
                    StartDate = startDate,
                    EndDate = endDate
                };

                var result = await _mediator.Send(query);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting migration statistics");
                return StatusCode(500, new { error = "An error occurred while getting migration statistics", details = ex.Message });
            }
        }

        /// <summary>
        /// Gets migration progress for a specific operation
        /// </summary>
        /// <param name="operationId">Migration operation ID</param>
        /// <returns>Migration progress information</returns>
        [HttpGet("progress/{operationId:guid}")]
        [ProducesResponseType(typeof(MigrationProgress), 200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(403)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<MigrationProgress>> GetMigrationProgressAsync(Guid operationId)
        {
            try
            {
                var query = new GetMigrationProgressQuery { OperationId = operationId };
                var result = await _mediator.Send(query);

                if (result == null)
                {
                    return NotFound(new { error = "Migration operation not found", operationId });
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting migration progress for operation {OperationId}", operationId);
                return StatusCode(500, new { error = "An error occurred while getting migration progress", details = ex.Message });
            }
        }

        /// <summary>
        /// Validates that a report has been successfully migrated
        /// </summary>
        /// <param name="reportId">Report ID to validate</param>
        /// <param name="versionId">Optional version ID to validate specific version</param>
        /// <param name="options">Validation options</param>
        /// <returns>Validation result with details</returns>
        [HttpPost("validate/{reportId:guid}")]
        [ProducesResponseType(typeof(ValidationResult), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(403)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ValidationResult>> ValidateMigrationAsync(
            Guid reportId,
            [FromQuery] Guid? versionId = null,
            [FromBody] ValidationOptions? options = null)
        {
            try
            {
                _logger.LogInformation("Validating migration for report {ReportId}, version {VersionId}. User: {UserId}", 
                    reportId, versionId, GetCurrentUserId());

                var command = new ValidateMigrationCommand
                {
                    ReportId = reportId,
                    VersionId = versionId,
                    Options = options ?? new ValidationOptions(),
                    InitiatedBy = GetCurrentUserId()
                };

                var result = await _mediator.Send(command);

                _logger.LogInformation("Migration validation completed. ReportId: {ReportId}, Valid: {IsValid}", 
                    reportId, result.IsValid);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating migration for report {ReportId}", reportId);
                return StatusCode(500, new { error = "An error occurred while validating the migration", details = ex.Message });
            }
        }

        /// <summary>
        /// Rolls back migration for a specific report
        /// </summary>
        /// <param name="reportId">Report ID to rollback</param>
        /// <param name="versionId">Optional version ID to rollback specific version</param>
        /// <param name="reason">Reason for rollback</param>
        /// <param name="options">Rollback options</param>
        /// <returns>Rollback result</returns>
        [HttpPost("rollback/{reportId:guid}")]
        [ProducesResponseType(typeof(RollbackResult), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(403)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<RollbackResult>> RollbackMigrationAsync(
            Guid reportId,
            [FromQuery] Guid? versionId = null,
            [FromQuery] string reason = "",
            [FromBody] RollbackOptions? options = null)
        {
            try
            {
                _logger.LogInformation("Rolling back migration for report {ReportId}, version {VersionId}. User: {UserId}, Reason: {Reason}", 
                    reportId, versionId, GetCurrentUserId(), reason);

                var command = new RollbackMigrationCommand
                {
                    ReportId = reportId,
                    VersionId = versionId,
                    Options = options ?? new RollbackOptions(),
                    InitiatedBy = GetCurrentUserId(),
                    Reason = reason
                };

                var result = await _mediator.Send(command);

                _logger.LogInformation("Migration rollback completed. ReportId: {ReportId}, Success: {Success}", 
                    reportId, result.Success);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rolling back migration for report {ReportId}", reportId);
                return StatusCode(500, new { error = "An error occurred while rolling back the migration", details = ex.Message });
            }
        }

        /// <summary>
        /// Performs a dry run migration without making actual changes
        /// </summary>
        /// <param name="reportId">Optional report ID for specific report dry run</param>
        /// <param name="options">Migration options</param>
        /// <returns>Dry run result with what would be migrated</returns>
        [HttpPost("dry-run")]
        [ProducesResponseType(typeof(DryRunResult), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(403)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<DryRunResult>> PerformDryRunAsync(
            [FromQuery] Guid? reportId = null,
            [FromBody] MigrationOptions? options = null)
        {
            try
            {
                _logger.LogInformation("Performing dry run migration. ReportId: {ReportId}, User: {UserId}", 
                    reportId, GetCurrentUserId());

                var command = new PerformDryRunCommand
                {
                    ReportId = reportId,
                    Options = options ?? new MigrationOptions(),
                    InitiatedBy = GetCurrentUserId()
                };

                var result = await _mediator.Send(command);

                _logger.LogInformation("Dry run completed. Reports to migrate: {ReportsToMigrate}", 
                    result.ReportsToMigrate);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing dry run migration");
                return StatusCode(500, new { error = "An error occurred while performing the dry run", details = ex.Message });
            }
        }

        /// <summary>
        /// Cancels an ongoing migration operation
        /// </summary>
        /// <param name="operationId">Migration operation ID to cancel</param>
        /// <param name="reason">Reason for cancellation</param>
        /// <param name="force">Whether to force cancellation even if operation is in critical phase</param>
        /// <returns>Cancellation result</returns>
        [HttpPost("cancel/{operationId:guid}")]
        [ProducesResponseType(typeof(CancellationResult), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(403)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<CancellationResult>> CancelMigrationAsync(
            Guid operationId,
            [FromQuery] string reason = "",
            [FromQuery] bool force = false)
        {
            try
            {
                _logger.LogInformation("Cancelling migration operation {OperationId}. User: {UserId}, Reason: {Reason}, Force: {Force}", 
                    operationId, GetCurrentUserId(), reason, force);

                var command = new CancelMigrationCommand
                {
                    OperationId = operationId,
                    InitiatedBy = GetCurrentUserId(),
                    Reason = reason,
                    Force = force
                };

                var result = await _mediator.Send(command);

                _logger.LogInformation("Migration cancellation completed. OperationId: {OperationId}, Success: {Success}", 
                    operationId, result.Success);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling migration operation {OperationId}", operationId);
                return StatusCode(500, new { error = "An error occurred while cancelling the migration", details = ex.Message });
            }
        }

        /// <summary>
        /// Gets a list of reports that need migration
        /// </summary>
        /// <param name="tenantId">Optional tenant ID filter</param>
        /// <param name="maxResults">Maximum number of reports to return</param>
        /// <param name="skip">Skip count for pagination</param>
        /// <param name="sortOrder">Sort order for results</param>
        /// <returns>List of reports requiring migration</returns>
        [HttpGet("reports/pending")]
        [ProducesResponseType(typeof(IEnumerable<ReportMigrationInfo>), 200)]
        [ProducesResponseType(401)]
        [ProducesResponseType(403)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<IEnumerable<ReportMigrationInfo>>> GetReportsRequiringMigrationAsync(
            [FromQuery] Guid? tenantId = null,
            [FromQuery] int maxResults = 100,
            [FromQuery] int skip = 0,
            [FromQuery] MigrationSortOrder sortOrder = MigrationSortOrder.Priority)
        {
            try
            {
                var query = new GetReportsRequiringMigrationQuery
                {
                    TenantId = tenantId,
                    MaxResults = Math.Min(maxResults, 1000), // Cap at 1000
                    Skip = Math.Max(skip, 0),
                    SortOrder = sortOrder
                };

                var result = await _mediator.Send(query);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting reports requiring migration");
                return StatusCode(500, new { error = "An error occurred while getting reports requiring migration", details = ex.Message });
            }
        }

        /// <summary>
        /// Gets migration history for a specific report
        /// </summary>
        /// <param name="reportId">Report ID</param>
        /// <param name="maxResults">Maximum number of history entries to return</param>
        /// <param name="skip">Skip count for pagination</param>
        /// <returns>Migration history entries</returns>
        [HttpGet("history/{reportId:guid}")]
        [ProducesResponseType(typeof(IEnumerable<MigrationHistoryEntry>), 200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(403)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<IEnumerable<MigrationHistoryEntry>>> GetMigrationHistoryAsync(
            Guid reportId,
            [FromQuery] int maxResults = 50,
            [FromQuery] int skip = 0)
        {
            try
            {
                var query = new GetMigrationHistoryQuery
                {
                    ReportId = reportId,
                    MaxResults = Math.Min(maxResults, 500), // Cap at 500
                    Skip = Math.Max(skip, 0)
                };

                var result = await _mediator.Send(query);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting migration history for report {ReportId}", reportId);
                return StatusCode(500, new { error = "An error occurred while getting migration history", details = ex.Message });
            }
        }

        /// <summary>
        /// Cleans up failed migration artifacts
        /// </summary>
        /// <param name="reportId">Optional report ID for specific cleanup</param>
        /// <param name="operationId">Optional operation ID for specific operation cleanup</param>
        /// <param name="options">Cleanup options</param>
        /// <returns>Cleanup result</returns>
        [HttpPost("cleanup")]
        [ProducesResponseType(typeof(CleanupResult), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(403)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<CleanupResult>> CleanupFailedMigrationAsync(
            [FromQuery] Guid? reportId = null,
            [FromQuery] Guid? operationId = null,
            [FromBody] CleanupOptions? options = null)
        {
            try
            {
                _logger.LogInformation("Cleaning up failed migration artifacts. ReportId: {ReportId}, OperationId: {OperationId}, User: {UserId}", 
                    reportId, operationId, GetCurrentUserId());

                var command = new CleanupFailedMigrationCommand
                {
                    ReportId = reportId,
                    OperationId = operationId,
                    Options = options ?? new CleanupOptions(),
                    InitiatedBy = GetCurrentUserId()
                };

                var result = await _mediator.Send(command);

                _logger.LogInformation("Migration cleanup completed. Success: {Success}, Items cleaned: {ItemsCleanedUp}", 
                    result.Success, result.ItemsCleanedUp);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up failed migration artifacts");
                return StatusCode(500, new { error = "An error occurred while cleaning up migration artifacts", details = ex.Message });
            }
        }

        /// <summary>
        /// Validates storage connectivity and permissions
        /// </summary>
        /// <param name="deepValidation">Whether to perform deep validation (slower but more thorough)</param>
        /// <param name="timeoutSeconds">Timeout for validation operations in seconds</param>
        /// <returns>Storage validation result</returns>
        [HttpPost("validate-storage")]
        [ProducesResponseType(typeof(StorageValidationResult), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(403)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<StorageValidationResult>> ValidateStorageConnectivityAsync(
            [FromQuery] bool deepValidation = false,
            [FromQuery] int timeoutSeconds = 30)
        {
            try
            {
                _logger.LogInformation("Validating storage connectivity. DeepValidation: {DeepValidation}, User: {UserId}", 
                    deepValidation, GetCurrentUserId());

                var command = new ValidateStorageConnectivityCommand
                {
                    DeepValidation = deepValidation,
                    TimeoutSeconds = Math.Min(timeoutSeconds, 300) // Cap at 5 minutes
                };

                var result = await _mediator.Send(command);

                _logger.LogInformation("Storage validation completed. AllStorageAccessible: {AllStorageAccessible}", 
                    result.AllStorageAccessible);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating storage connectivity");
                return StatusCode(500, new { error = "An error occurred while validating storage connectivity", details = ex.Message });
            }
        }

        /// <summary>
        /// Creates a backup of report data before migration
        /// </summary>
        /// <param name="reportId">Optional report ID for specific report backup</param>
        /// <param name="options">Backup options</param>
        /// <returns>Backup result</returns>
        [HttpPost("backup")]
        [ProducesResponseType(typeof(BackupResult), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(403)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<BackupResult>> BackupReportDataAsync(
            [FromQuery] Guid? reportId = null,
            [FromBody] BackupOptions? options = null)
        {
            try
            {
                _logger.LogInformation("Creating backup of report data. ReportId: {ReportId}, User: {UserId}", 
                    reportId, GetCurrentUserId());

                var command = new BackupReportDataCommand
                {
                    ReportId = reportId,
                    Options = options ?? new BackupOptions(),
                    InitiatedBy = GetCurrentUserId()
                };

                var result = await _mediator.Send(command);

                _logger.LogInformation("Backup completed. Success: {Success}, BackupId: {BackupId}, Size: {BackupSize}", 
                    result.Success, result.BackupId, result.BackupSize);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating backup of report data");
                return StatusCode(500, new { error = "An error occurred while creating the backup", details = ex.Message });
            }
        }

        /// <summary>
        /// Restores report data from a backup
        /// </summary>
        /// <param name="backupId">Backup ID to restore from</param>
        /// <param name="reportId">Optional report ID for specific report restore</param>
        /// <param name="options">Restore options</param>
        /// <returns>Restore result</returns>
        [HttpPost("restore/{backupId:guid}")]
        [ProducesResponseType(typeof(RestoreResult), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(403)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<RestoreResult>> RestoreReportDataAsync(
            Guid backupId,
            [FromQuery] Guid? reportId = null,
            [FromBody] RestoreOptions? options = null)
        {
            try
            {
                _logger.LogInformation("Restoring report data from backup. BackupId: {BackupId}, ReportId: {ReportId}, User: {UserId}", 
                    backupId, reportId, GetCurrentUserId());

                var command = new RestoreReportDataCommand
                {
                    BackupId = backupId,
                    ReportId = reportId,
                    Options = options ?? new RestoreOptions(),
                    InitiatedBy = GetCurrentUserId()
                };

                var result = await _mediator.Send(command);

                _logger.LogInformation("Restore completed. Success: {Success}, Reports restored: {ReportsRestored}", 
                    result.Success, result.ReportsRestored);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error restoring report data from backup {BackupId}", backupId);
                return StatusCode(500, new { error = "An error occurred while restoring from backup", details = ex.Message });
            }
        }
    }
}
