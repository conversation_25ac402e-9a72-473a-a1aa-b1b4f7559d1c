# PowerShell script to diagnose foreign key mismatches between Reports and ReportVersions
param(
    [string]$ReportsJsonPath = "FY.WB.CSHero2.Infrastructure\Persistence\SeedData\reports.json",
    [string]$ReportVersionsJsonPath = "FY.WB.CSHero2.Infrastructure\Persistence\SeedData\report-versions.json"
)

Write-Host "Diagnosing foreign key mismatches..." -ForegroundColor Green

try {
    # Read both JSON files
    $reportsContent = Get-Content $ReportsJsonPath -Raw
    $reports = $reportsContent | ConvertFrom-Json
    
    $reportVersionsContent = Get-Content $ReportVersionsJsonPath -Raw
    $reportVersions = $reportVersionsContent | ConvertFrom-Json
    
    Write-Host "Found $($reports.Count) reports and $($reportVersions.Count) report versions" -ForegroundColor Cyan
    
    # Create lookup of Report IDs
    $reportIds = @{}
    foreach ($report in $reports) {
        $reportIds[$report.id] = $report.name
    }
    
    Write-Host "`nReport IDs in reports.json:" -ForegroundColor Yellow
    for ($i = 0; $i -lt [Math]::Min(10, $reports.Count); $i++) {
        Write-Host "  [$i] $($reports[$i].id) - $($reports[$i].name)" -ForegroundColor White
    }
    if ($reports.Count -gt 10) {
        Write-Host "  ... and $($reports.Count - 10) more" -ForegroundColor Gray
    }
    
    Write-Host "`nReportVersion ReportIds in report-versions.json:" -ForegroundColor Yellow
    $mismatches = @()
    for ($i = 0; $i -lt [Math]::Min(10, $reportVersions.Count); $i++) {
        $reportId = $reportVersions[$i].reportId
        $exists = $reportIds.ContainsKey($reportId)
        $status = if ($exists) { "✓" } else { "✗"; $mismatches += $i }
        Write-Host "  [$i] $reportId $status" -ForegroundColor $(if ($exists) { "Green" } else { "Red" })
    }
    if ($reportVersions.Count -gt 10) {
        Write-Host "  ... and $($reportVersions.Count - 10) more" -ForegroundColor Gray
    }
    
    # Check for mismatches
    Write-Host "`nForeign Key Validation:" -ForegroundColor Cyan
    $allValid = $true
    $invalidCount = 0
    
    foreach ($version in $reportVersions) {
        if (-not $reportIds.ContainsKey($version.reportId)) {
            Write-Host "  ✗ ReportVersion $($version.id) references non-existent Report $($version.reportId)" -ForegroundColor Red
            $allValid = $false
            $invalidCount++
        }
    }
    
    if ($allValid) {
        Write-Host "  ✓ All ReportVersions reference valid Reports!" -ForegroundColor Green
    } else {
        Write-Host "  ✗ Found $invalidCount invalid foreign key references" -ForegroundColor Red
    }
    
    # Show first few mismatches in detail
    if ($mismatches.Count -gt 0) {
        Write-Host "`nFirst few mismatches:" -ForegroundColor Red
        for ($i = 0; $i -lt [Math]::Min(5, $mismatches.Count); $i++) {
            $idx = $mismatches[$i]
            $version = $reportVersions[$idx]
            Write-Host "  Index $idx`: ReportVersion $($version.id) -> Report $($version.reportId) (NOT FOUND)" -ForegroundColor Red
        }
    }
    
    # Show what Reports exist but have no versions
    Write-Host "`nReports without ReportVersions:" -ForegroundColor Yellow
    $versionReportIds = $reportVersions | ForEach-Object { $_.reportId }
    $orphanedReports = @()
    
    foreach ($report in $reports) {
        if ($versionReportIds -notcontains $report.id) {
            $orphanedReports += $report
        }
    }
    
    if ($orphanedReports.Count -eq 0) {
        Write-Host "  ✓ All Reports have corresponding ReportVersions" -ForegroundColor Green
    } else {
        Write-Host "  Found $($orphanedReports.Count) Reports without ReportVersions:" -ForegroundColor Red
        for ($i = 0; $i -lt [Math]::Min(5, $orphanedReports.Count); $i++) {
            Write-Host "    $($orphanedReports[$i].id) - $($orphanedReports[$i].name)" -ForegroundColor Red
        }
        if ($orphanedReports.Count -gt 5) {
            Write-Host "    ... and $($orphanedReports.Count - 5) more" -ForegroundColor Gray
        }
    }
    
    Write-Host "`nSummary:" -ForegroundColor Cyan
    Write-Host "  Reports: $($reports.Count)" -ForegroundColor White
    Write-Host "  ReportVersions: $($reportVersions.Count)" -ForegroundColor White
    Write-Host "  Invalid FK references: $invalidCount" -ForegroundColor $(if ($invalidCount -eq 0) { "Green" } else { "Red" })
    Write-Host "  Reports without versions: $($orphanedReports.Count)" -ForegroundColor $(if ($orphanedReports.Count -eq 0) { "Green" } else { "Yellow" })
}
catch {
    Write-Error "Error during diagnosis: $($_.Exception.Message)"
    exit 1
}
