﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FY.WB.CSHero2.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddReportRenderingV2EntitiesPhase1 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "DefaultStyleJson",
                table: "Templates",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "{}");

            migrationBuilder.AddColumn<int>(
                name: "EstimatedCompletionTimeMinutes",
                table: "Templates",
                type: "int",
                nullable: false,
                defaultValue: 30);

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "Templates",
                type: "bit",
                nullable: false,
                defaultValue: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsPublic",
                table: "Templates",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "UsageCount",
                table: "Templates",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "Version",
                table: "Templates",
                type: "nvarchar(20)",
                maxLength: 20,
                nullable: false,
                defaultValue: "1.0.0");

            migrationBuilder.AddColumn<Guid>(
                name: "CurrentVersionId",
                table: "Reports",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ReportType",
                table: "Reports",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "Standard");

            migrationBuilder.AddColumn<Guid>(
                name: "TemplateId",
                table: "Reports",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "ReportVersions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ReportId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    VersionNumber = table.Column<int>(type: "int", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ComponentDataJson = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    JsonData = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    IsCurrent = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    ComponentDataSize = table.Column<long>(type: "bigint", nullable: false, defaultValue: 0L),
                    JsonDataSize = table.Column<long>(type: "bigint", nullable: false, defaultValue: 0L),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ReportVersions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ReportVersions_Reports_ReportId",
                        column: x => x.ReportId,
                        principalTable: "Reports",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ComponentDefinitions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ReportVersionId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    SectionId = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    SectionName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    ComponentCode = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    TypeDefinitions = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ImportsJson = table.Column<string>(type: "nvarchar(max)", nullable: false, defaultValue: "[]"),
                    MetadataJson = table.Column<string>(type: "nvarchar(max)", nullable: false, defaultValue: "{}"),
                    GeneratedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    GeneratedBy = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ComponentHash = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    ComponentSize = table.Column<long>(type: "bigint", nullable: false, defaultValue: 0L),
                    IsValid = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    ValidationErrors = table.Column<string>(type: "nvarchar(max)", nullable: false, defaultValue: ""),
                    Framework = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, defaultValue: "NextJS"),
                    StyleFramework = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, defaultValue: "TailwindCSS"),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ComponentDefinitions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ComponentDefinitions_ReportVersions_ReportVersionId",
                        column: x => x.ReportVersionId,
                        principalTable: "ReportVersions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Templates_IsActive",
                table: "Templates",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_Templates_IsPublic",
                table: "Templates",
                column: "IsPublic");

            migrationBuilder.CreateIndex(
                name: "IX_Templates_IsPublic_IsActive_Category",
                table: "Templates",
                columns: new[] { "IsPublic", "IsActive", "Category" });

            migrationBuilder.CreateIndex(
                name: "IX_Reports_CurrentVersionId",
                table: "Reports",
                column: "CurrentVersionId");

            migrationBuilder.CreateIndex(
                name: "IX_Reports_ReportType",
                table: "Reports",
                column: "ReportType");

            migrationBuilder.CreateIndex(
                name: "IX_Reports_TemplateId",
                table: "Reports",
                column: "TemplateId");

            migrationBuilder.CreateIndex(
                name: "IX_Reports_TemplateId_ReportType",
                table: "Reports",
                columns: new[] { "TemplateId", "ReportType" });

            migrationBuilder.CreateIndex(
                name: "IX_ComponentDefinitions_ComponentHash",
                table: "ComponentDefinitions",
                column: "ComponentHash");

            migrationBuilder.CreateIndex(
                name: "IX_ComponentDefinitions_GeneratedAt",
                table: "ComponentDefinitions",
                column: "GeneratedAt");

            migrationBuilder.CreateIndex(
                name: "IX_ComponentDefinitions_IsValid",
                table: "ComponentDefinitions",
                column: "IsValid");

            migrationBuilder.CreateIndex(
                name: "IX_ComponentDefinitions_ReportVersionId",
                table: "ComponentDefinitions",
                column: "ReportVersionId");

            migrationBuilder.CreateIndex(
                name: "IX_ComponentDefinitions_ReportVersionId_SectionId",
                table: "ComponentDefinitions",
                columns: new[] { "ReportVersionId", "SectionId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ComponentDefinitions_SectionId",
                table: "ComponentDefinitions",
                column: "SectionId");

            migrationBuilder.CreateIndex(
                name: "IX_ReportVersions_CreatedAt",
                table: "ReportVersions",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_ReportVersions_IsCurrent",
                table: "ReportVersions",
                column: "IsCurrent");

            migrationBuilder.CreateIndex(
                name: "IX_ReportVersions_ReportId",
                table: "ReportVersions",
                column: "ReportId");

            migrationBuilder.CreateIndex(
                name: "IX_ReportVersions_ReportId_VersionNumber",
                table: "ReportVersions",
                columns: new[] { "ReportId", "VersionNumber" },
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_Reports_Templates_TemplateId",
                table: "Reports",
                column: "TemplateId",
                principalTable: "Templates",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Reports_Templates_TemplateId",
                table: "Reports");

            migrationBuilder.DropTable(
                name: "ComponentDefinitions");

            migrationBuilder.DropTable(
                name: "ReportVersions");

            migrationBuilder.DropIndex(
                name: "IX_Templates_IsActive",
                table: "Templates");

            migrationBuilder.DropIndex(
                name: "IX_Templates_IsPublic",
                table: "Templates");

            migrationBuilder.DropIndex(
                name: "IX_Templates_IsPublic_IsActive_Category",
                table: "Templates");

            migrationBuilder.DropIndex(
                name: "IX_Reports_CurrentVersionId",
                table: "Reports");

            migrationBuilder.DropIndex(
                name: "IX_Reports_ReportType",
                table: "Reports");

            migrationBuilder.DropIndex(
                name: "IX_Reports_TemplateId",
                table: "Reports");

            migrationBuilder.DropIndex(
                name: "IX_Reports_TemplateId_ReportType",
                table: "Reports");

            migrationBuilder.DropColumn(
                name: "DefaultStyleJson",
                table: "Templates");

            migrationBuilder.DropColumn(
                name: "EstimatedCompletionTimeMinutes",
                table: "Templates");

            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "Templates");

            migrationBuilder.DropColumn(
                name: "IsPublic",
                table: "Templates");

            migrationBuilder.DropColumn(
                name: "UsageCount",
                table: "Templates");

            migrationBuilder.DropColumn(
                name: "Version",
                table: "Templates");

            migrationBuilder.DropColumn(
                name: "CurrentVersionId",
                table: "Reports");

            migrationBuilder.DropColumn(
                name: "ReportType",
                table: "Reports");

            migrationBuilder.DropColumn(
                name: "TemplateId",
                table: "Reports");
        }
    }
}
