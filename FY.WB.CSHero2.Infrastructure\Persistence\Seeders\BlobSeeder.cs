using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;
using System.Text;
using FY.WB.CSHero2.Domain.Entities;

namespace FY.WB.CSHero2.Infrastructure.Persistence.Seeders
{
    public interface IBlobSeeder
    {
        Task SeedAsync(ApplicationDbContext context, CancellationToken cancellationToken = default);
        Task<List<string>> GetExistingBlobIdsAsync(CancellationToken cancellationToken = default);
    }

    public class BlobSeeder : IBlobSeeder
    {
        private readonly ILogger<BlobSeeder> _logger;

        public BlobSeeder(ILogger<BlobSeeder> logger)
        {
            _logger = logger;
        }

        public async Task SeedAsync(ApplicationDbContext context, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Starting Blob Storage seeding...");

            try
            {
                // Get all report versions for blob creation
                var reportVersions = await context.ReportVersions
                    .IgnoreQueryFilters()
                    .Include(rv => rv.Report)
                    .ToListAsync(cancellationToken);

                var createdCount = 0;
                var skippedCount = 0;
                var errorCount = 0;

                foreach (var version in reportVersions)
                {
                    try
                    {
                        // Create components blob for this version
                        var componentsBlobId = await CreateComponentsBlobAsync(context, version, cancellationToken);
                        if (!string.IsNullOrEmpty(componentsBlobId))
                        {
                            version.ComponentsBlobId = componentsBlobId;
                            createdCount++;
                        }

                        // Create styles blob for this version
                        var stylesBlobId = await CreateStylesBlobAsync(context, version, cancellationToken);
                        if (!string.IsNullOrEmpty(stylesBlobId))
                        {
                            version.StylesBlobId = stylesBlobId;
                            version.StylesSize = CalculateStylesSize(version);
                            createdCount++;
                        }

                        // Update storage strategy to indicate blob storage is now available
                        version.StorageStrategy = "MultiStorage";
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error seeding blobs for report version {VersionId}", version.Id);
                        errorCount++;
                    }
                }

                // Save all changes
                await context.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Blob Storage seeding completed: {Created} blobs created, {Skipped} skipped, {Errors} errors",
                    createdCount, skippedCount, errorCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during Blob Storage seeding");
                throw;
            }
        }

        public async Task<List<string>> GetExistingBlobIdsAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                // TODO: Implement blob ID retrieval when blob storage services are available
                _logger.LogDebug("Blob Storage ID retrieval not yet implemented - returning empty list");
                await Task.CompletedTask;
                return new List<string>();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error getting existing blob IDs, returning empty list");
                return new List<string>();
            }
        }

        private async Task<string> CreateComponentsBlobAsync(
            ApplicationDbContext context,
            ReportVersion version,
            CancellationToken cancellationToken)
        {
            try
            {
                // Create components blob structure according to handoff document
                // Storage hierarchy: /tenants/{tenant-id}/reports/{report-id}/versions/v{version-number}/components.json

                var tenantId = version.Report?.TenantId?.ToString() ?? "default";
                var reportId = version.ReportId;
                var versionNumber = version.VersionNumber;

                var componentsBlob = new ComponentsBlob
                {
                    ReportId = reportId,
                    VersionId = version.Id,
                    VersionNumber = versionNumber,
                    TenantId = tenantId,
                    ComponentDataJson = version.ComponentDataJson,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = "system"
                };

                // Generate blob ID following the hierarchy pattern
                var blobId = $"tenants/{tenantId}/reports/{reportId}/versions/v{versionNumber}/components.json";

                // TODO: When blob storage service is available, save the blob
                // For now, just simulate the blob creation
                _logger.LogDebug("Created components blob {BlobId} for version {VersionId}", blobId, version.Id);

                return blobId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating components blob for version {VersionId}", version.Id);
                return string.Empty;
            }
        }

        private async Task<string> CreateStylesBlobAsync(
            ApplicationDbContext context,
            ReportVersion version,
            CancellationToken cancellationToken)
        {
            try
            {
                // Create styles blob structure according to handoff document
                // Storage hierarchy: /tenants/{tenant-id}/reports/{report-id}/versions/v{version-number}/styles.json

                var tenantId = version.Report?.TenantId?.ToString() ?? "default";
                var reportId = version.ReportId;
                var versionNumber = version.VersionNumber;

                var stylesBlob = new StylesBlob
                {
                    ReportId = reportId,
                    VersionId = version.Id,
                    VersionNumber = versionNumber,
                    TenantId = tenantId,
                    StyleDocumentId = version.StyleDocumentId,
                    CreatedAt = DateTime.UtcNow,
                    CreatedBy = "system"
                };

                // Generate blob ID following the hierarchy pattern
                var blobId = $"tenants/{tenantId}/reports/{reportId}/versions/v{versionNumber}/styles.json";

                // TODO: When blob storage service is available, save the blob
                // For now, just simulate the blob creation
                _logger.LogDebug("Created styles blob {BlobId} for version {VersionId}", blobId, version.Id);

                return blobId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating styles blob for version {VersionId}", version.Id);
                return string.Empty;
            }
        }

        private long CalculateStylesSize(ReportVersion version)
        {
            // Calculate approximate size of styles data
            var stylesData = new
            {
                StyleDocumentId = version.StyleDocumentId,
                VersionId = version.Id,
                CreatedAt = DateTime.UtcNow
            };

            var json = JsonSerializer.Serialize(stylesData);
            return Encoding.UTF8.GetByteCount(json);
        }

        // Blob data models
        public class ComponentsBlob
        {
            public Guid ReportId { get; set; }
            public Guid VersionId { get; set; }
            public int VersionNumber { get; set; }
            public string TenantId { get; set; } = string.Empty;
            public string ComponentDataJson { get; set; } = string.Empty;
            public DateTime CreatedAt { get; set; }
            public string CreatedBy { get; set; } = string.Empty;
        }

        public class StylesBlob
        {
            public Guid ReportId { get; set; }
            public Guid VersionId { get; set; }
            public int VersionNumber { get; set; }
            public string TenantId { get; set; } = string.Empty;
            public string? StyleDocumentId { get; set; }
            public DateTime CreatedAt { get; set; }
            public string CreatedBy { get; set; } = string.Empty;
        }
    }
}
