using System;
using System.Collections.Generic;
using FY.WB.CSHero2.Domain.Entities.Core;

namespace FY.WB.CSHero2.Domain.Entities
{
    /// <summary>
    /// Represents a section within a report (e.g., Executive Summary, Financial Analysis)
    /// </summary>
    public class ReportSection : AuditedEntity<Guid>
    {
        /// <summary>
        /// Foreign key to the parent report
        /// </summary>
        public Guid ReportId { get; set; }
        
        /// <summary>
        /// Title of the section (e.g., "Executive Summary", "Financial Analysis")
        /// </summary>
        public string Title { get; set; } = string.Empty;
        
        /// <summary>
        /// Type of the section (e.g., "text", "chart", "table", "list", "timeline")
        /// </summary>
        public string Type { get; set; } = string.Empty;
        
        /// <summary>
        /// Order of the section within the report (for sorting)
        /// </summary>
        public int Order { get; set; }

        /// <summary>
        /// Whether this section has been migrated to Cosmos DB
        /// </summary>
        public bool IsMigratedToCosmos { get; set; } = false;

        /// <summary>
        /// Reference to Cosmos DB document containing section data
        /// </summary>
        public string? CosmosDocumentId { get; set; }

        /// <summary>
        /// When this section was migrated to Cosmos DB
        /// </summary>
        public DateTime? MigrationDate { get; set; }

        /// <summary>
        /// Reference to the template section this was created from
        /// </summary>
        public Guid? TemplateSourceSectionId { get; set; }

        /// <summary>
        /// Whether this section has been modified from its template source
        /// </summary>
        public bool IsModifiedFromTemplate { get; set; } = false;
        
        // Navigation properties
        /// <summary>
        /// Navigation property to the parent report
        /// </summary>
        public virtual Report Report { get; set; } = null!;
        
        /// <summary>
        /// Navigation property to the fields within this section
        /// </summary>
        public virtual ICollection<ReportSectionField> Fields { get; set; } = new List<ReportSectionField>();

        /// <summary>
        /// Default constructor for EF Core
        /// </summary>
        public ReportSection() : base() { }

        /// <summary>
        /// Constructor with parameters
        /// </summary>
        public ReportSection(
            Guid id,
            Guid reportId,
            string title,
            string type,
            int order)
            : base(id)
        {
            ReportId = reportId;
            Title = title;
            Type = type;
            Order = order;
        }

        /// <summary>
        /// Updates the section details
        /// </summary>
        public void UpdateDetails(string title, string type, int order)
        {
            Title = title;
            Type = type;
            Order = order;
            // LastModificationTime and LastModifierId will be set by DbContext
        }

        /// <summary>
        /// Updates the section order
        /// </summary>
        public void UpdateOrder(int order)
        {
            Order = order;
            // LastModificationTime and LastModifierId will be set by DbContext
        }
    }
}
