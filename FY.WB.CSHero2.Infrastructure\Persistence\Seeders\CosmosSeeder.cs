using FY.WB.CSHero2.Domain.Entities;
using FY.WB.CSHero2.Infrastructure.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace FY.WB.CSHero2.Infrastructure.Persistence.Seeders
{
    public interface ICosmosSeeder
    {
        Task SeedAsync(ApplicationDbContext context, CancellationToken cancellationToken = default);
        Task<List<string>> GetExistingDocumentIdsAsync(CancellationToken cancellationToken = default);
    }

    public class CosmosSeeder : ICosmosSeeder
    {
        private readonly ICosmosDbService? _cosmosDbService;
        private readonly ILogger<CosmosSeeder> _logger;
        private static readonly JsonSerializerOptions JsonOptions = new()
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        };

        public CosmosSeeder(ICosmosDbService? cosmosDbService, ILogger<CosmosSeeder> logger)
        {
            _cosmosDbService = cosmosDbService;
            _logger = logger;
        }

        public async Task SeedAsync(ApplicationDbContext context, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Starting Cosmos DB seeding...");

            if (_cosmosDbService == null)
            {
                _logger.LogWarning("Cosmos DB service not available, skipping Cosmos DB seeding");
                return;
            }

            try
            {
                // Get existing document IDs to avoid duplicates
                var existingDocumentIds = await GetExistingDocumentIdsAsync(cancellationToken);
                _logger.LogInformation("Found {ExistingCount} existing documents in Cosmos DB", existingDocumentIds.Count);

                // Get all reports with their sections and fields from SQL
                var reports = await GetReportsWithDataAsync(context, cancellationToken);
                _logger.LogInformation("Found {ReportCount} reports to process for Cosmos DB seeding", reports.Count);

                var createdCount = 0;
                var skippedCount = 0;
                var errorCount = 0;

                foreach (var report in reports)
                {
                    try
                    {
                        var documentId = GenerateDocumentId(report.Id);
                        
                        if (existingDocumentIds.Contains(documentId))
                        {
                            _logger.LogDebug("Skipping report {ReportId} - document already exists", report.Id);
                            skippedCount++;
                            continue;
                        }

                        // Create document from report data
                        var document = await CreateReportDocumentAsync(context, report, cancellationToken);
                        
                        // Validate partition key
                        if (string.IsNullOrEmpty(document.TenantId))
                        {
                            _logger.LogError("Cannot seed report {ReportId} - TenantId is null or empty", report.Id);
                            errorCount++;
                            continue;
                        }
                        
                        // Save to Cosmos DB using document's TenantId as partition key
                        await _cosmosDbService.UpsertItemAsync(document, document.TenantId);
                        
                        // Update SQL entity with document ID
                        await UpdateReportWithDocumentIdAsync(context, report.Id, documentId, cancellationToken);
                        
                        createdCount++;
                        _logger.LogDebug("Created Cosmos document for report {ReportId} with partition key {PartitionKey}", 
                            report.Id, document.TenantId);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error seeding report {ReportId} to Cosmos DB", report.Id);
                        errorCount++;
                        // Continue with other reports instead of failing completely
                    }
                }

                _logger.LogInformation("Cosmos DB seeding completed: {Created} created, {Skipped} skipped, {Errors} errors", 
                    createdCount, skippedCount, errorCount);
                    
                if (errorCount > 0)
                {
                    _logger.LogWarning("Cosmos DB seeding completed with {ErrorCount} errors. Check logs for details.", errorCount);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Critical error during Cosmos DB seeding");
                throw;
            }
        }

        public async Task<List<string>> GetExistingDocumentIdsAsync(CancellationToken cancellationToken = default)
        {
            if (_cosmosDbService == null)
            {
                _logger.LogDebug("Cosmos DB service not available, returning empty document list");
                return new List<string>();
            }

            try
            {
                // Query all documents across all partitions to get their IDs
                var query = "SELECT c.id, c.tenantId FROM c WHERE STARTSWITH(c.id, 'report-data-')";
                var documents = await _cosmosDbService.GetItemsAsync<DocumentIdResult>(query);
                
                var documentIds = documents.Select(d => d.Id).ToList();
                _logger.LogDebug("Found {Count} existing documents across all partitions", documentIds.Count);
                
                return documentIds;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error getting existing document IDs from Cosmos DB, assuming empty");
                return new List<string>();
            }
        }

        private async Task<List<Report>> GetReportsWithDataAsync(ApplicationDbContext context, CancellationToken cancellationToken)
        {
            // Get reports and their sections separately due to navigation property structure
            var reports = await context.Reports
                .IgnoreQueryFilters()
                .ToListAsync(cancellationToken);

            // Load sections for each report
            foreach (var report in reports)
            {
                var sections = await context.ReportSections
                    .IgnoreQueryFilters()
                    .Where(rs => rs.ReportId == report.Id)
                    .Include(rs => rs.Fields)
                    .ToListAsync(cancellationToken);
                
                // Manually set the sections collection
                report.Sections = sections;
            }

            return reports;
        }

        private async Task<ReportDataDocument> CreateReportDocumentAsync(ApplicationDbContext context, Report report, CancellationToken cancellationToken)
        {
            // Get report versions for this report
            var reportVersions = await context.ReportVersions
                .IgnoreQueryFilters()
                .Where(rv => rv.ReportId == report.Id)
                .ToListAsync(cancellationToken);

            // Get the latest version or create a default one
            var latestVersion = reportVersions.OrderByDescending(rv => rv.VersionNumber).FirstOrDefault();
            var versionId = latestVersion?.Id ?? Guid.NewGuid();

            // Ensure TenantId is not null/empty and convert to string for partition key
            var tenantId = report.TenantId?.ToString() ?? "default";
            if (string.IsNullOrEmpty(tenantId) || tenantId == "00000000-0000-0000-0000-000000000000")
            {
                tenantId = "default";
                _logger.LogWarning("Report {ReportId} has null/empty TenantId, using 'default'", report.Id);
            }

            // Transform sections and fields into document format
            var sections = report.Sections?.Select(section => new ReportDataSection
            {
                Id = section.Id,
                Name = section.Title, // ReportSection uses Title, not Name
                Title = section.Title,
                Description = string.Empty, // ReportSection doesn't have Description
                SectionType = section.Type, // ReportSection uses Type, not SectionType
                DisplayOrder = section.Order, // ReportSection uses Order, not DisplayOrder
                IsRequired = false, // ReportSection doesn't have IsRequired
                Fields = section.Fields?.Select(field => new ReportDataField
                {
                    Id = field.Id,
                    Name = field.Name,
                    Label = field.Name, // ReportSectionField doesn't have Label, use Name
                    FieldType = field.Type, // ReportSectionField uses Type, not FieldType
                    DefaultValue = field.Content ?? string.Empty, // ReportSectionField uses Content, not DefaultValue
                    IsRequired = false, // ReportSectionField doesn't have IsRequired
                    DisplayOrder = field.Order, // ReportSectionField uses Order, not DisplayOrder
                    ValidationRules = new Dictionary<string, object>(), // ReportSectionField doesn't have ValidationRules
                    Options = new List<string>() // ReportSectionField doesn't have Options
                }).ToList() ?? new List<ReportDataField>()
            }).ToList() ?? new List<ReportDataSection>();

            return new ReportDataDocument
            {
                Id = GenerateDocumentId(report.Id),
                TenantId = tenantId, // Fixed: Use string tenantId as partition key
                ReportId = report.Id,
                VersionId = versionId,
                ReportName = report.Name,
                ReportNumber = report.ReportNumber,
                Category = report.Category,
                Status = report.Status,
                Author = report.Author,
                ClientId = report.ClientId,
                ClientName = report.ClientName,
                Sections = sections,
                Metadata = new ReportDataMetadata
                {
                    CreatedAt = report.CreationTime,
                    UpdatedAt = report.LastModificationTime ?? report.CreationTime,
                    SectionCount = sections.Count,
                    FieldCount = sections.Sum(s => s.Fields.Count),
                    Version = latestVersion?.VersionNumber.ToString() ?? "1",
                    Tags = new List<string> { report.Category, report.Status }
                }
            };
        }

        private async Task UpdateReportWithDocumentIdAsync(ApplicationDbContext context, Guid reportId, string documentId, CancellationToken cancellationToken)
        {
            var report = await context.Reports
                .IgnoreQueryFilters()
                .FirstOrDefaultAsync(r => r.Id == reportId, cancellationToken);

            if (report != null)
            {
                report.DataDocumentId = documentId;
                await context.SaveChangesAsync(cancellationToken);
            }
        }

        private static string GenerateDocumentId(Guid reportId)
        {
            return $"report-data-{reportId}";
        }

        // Document models for Cosmos DB
        public class ReportDataDocument
        {
            [JsonPropertyName("id")]
            public string Id { get; set; } = string.Empty;
            
            [JsonPropertyName("tenantId")]
            public string TenantId { get; set; } = string.Empty; // Changed to string for partition key
            
            [JsonPropertyName("reportId")]
            public Guid ReportId { get; set; }
            
            [JsonPropertyName("versionId")]
            public Guid VersionId { get; set; }
            
            [JsonPropertyName("reportName")]
            public string ReportName { get; set; } = string.Empty;
            
            [JsonPropertyName("reportNumber")]
            public string ReportNumber { get; set; } = string.Empty;
            
            [JsonPropertyName("category")]
            public string Category { get; set; } = string.Empty;
            
            [JsonPropertyName("status")]
            public string Status { get; set; } = string.Empty;
            
            [JsonPropertyName("author")]
            public string Author { get; set; } = string.Empty;
            
            [JsonPropertyName("clientId")]
            public Guid ClientId { get; set; }
            
            [JsonPropertyName("clientName")]
            public string ClientName { get; set; } = string.Empty;
            
            [JsonPropertyName("sections")]
            public List<ReportDataSection> Sections { get; set; } = new();
            
            [JsonPropertyName("metadata")]
            public ReportDataMetadata Metadata { get; set; } = new();
        }

        public class ReportDataSection
        {
            public Guid Id { get; set; }
            public string Name { get; set; } = string.Empty;
            public string Title { get; set; } = string.Empty;
            public string Description { get; set; } = string.Empty;
            public string SectionType { get; set; } = string.Empty;
            public int DisplayOrder { get; set; }
            public bool IsRequired { get; set; }
            public List<ReportDataField> Fields { get; set; } = new();
        }

        public class ReportDataField
        {
            public Guid Id { get; set; }
            public string Name { get; set; } = string.Empty;
            public string Label { get; set; } = string.Empty;
            public string FieldType { get; set; } = string.Empty;
            public string DefaultValue { get; set; } = string.Empty;
            public bool IsRequired { get; set; }
            public int DisplayOrder { get; set; }
            public Dictionary<string, object> ValidationRules { get; set; } = new();
            public List<string> Options { get; set; } = new();
        }

        public class ReportDataMetadata
        {
            public DateTime CreatedAt { get; set; }
            public DateTime UpdatedAt { get; set; }
            public int SectionCount { get; set; }
            public int FieldCount { get; set; }
            public string Version { get; set; } = string.Empty;
            public List<string> Tags { get; set; } = new();
        }

        // Helper class for Cosmos DB query results
        public class DocumentIdResult
        {
            [JsonPropertyName("id")]
            public string Id { get; set; } = string.Empty;
            
            [JsonPropertyName("tenantId")]
            public string TenantId { get; set; } = string.Empty;
        }
    }
}
