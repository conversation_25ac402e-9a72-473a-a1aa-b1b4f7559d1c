# PowerShell script to fix ReportVersion foreign key references
param(
    [string]$ReportsJsonPath = "FY.WB.CSHero2.Infrastructure\Persistence\SeedData\reports.json",
    [string]$ReportVersionsJsonPath = "FY.WB.CSHero2.Infrastructure\Persistence\SeedData\report-versions.json"
)

Write-Host "Starting ReportVersions foreign key fix..." -ForegroundColor Green

# Check if files exist
if (-not (Test-Path $ReportsJsonPath)) {
    Write-Error "Reports file not found: $ReportsJsonPath"
    exit 1
}

if (-not (Test-Path $ReportVersionsJsonPath)) {
    Write-Error "ReportVersions file not found: $ReportVersionsJsonPath"
    exit 1
}

# Create backup
$backupPath = $ReportVersionsJsonPath + ".backup." + (Get-Date -Format "yyyyMMdd_HHmmss")
Copy-Item $ReportVersionsJsonPath $backupPath
Write-Host "Backup created: $backupPath" -ForegroundColor Yellow

try {
    # Read both JSON files
    $reportsContent = Get-Content $ReportsJsonPath -Raw
    $reports = $reportsContent | ConvertFrom-Json
    
    $reportVersionsContent = Get-Content $ReportVersionsJsonPath -Raw
    $reportVersions = $reportVersionsContent | ConvertFrom-Json
    
    Write-Host "Found $($reports.Count) reports and $($reportVersions.Count) report versions" -ForegroundColor Cyan
    
    # Create a list to hold the updated report versions
    $updatedReportVersions = @()
    
    # Function to generate new GUID
    function New-ValidGuid {
        return [System.Guid]::NewGuid().ToString()
    }
    
    # Update existing ReportVersions to match Reports (by index)
    $existingVersionsCount = [Math]::Min($reports.Count, $reportVersions.Count)
    
    Write-Host "Updating $existingVersionsCount existing ReportVersions..." -ForegroundColor Cyan
    
    for ($i = 0; $i -lt $existingVersionsCount; $i++) {
        $reportVersion = $reportVersions[$i]
        $report = $reports[$i]
        
        # Update the reportId to match the corresponding report
        $oldReportId = $reportVersion.reportId
        $newReportId = $report.id
        
        if ($oldReportId -ne $newReportId) {
            Write-Host "  Index $i`: Updating reportId from $oldReportId to $newReportId" -ForegroundColor Yellow
            $reportVersion.reportId = $newReportId
        }
        
        $updatedReportVersions += $reportVersion
    }
    
    # Generate new ReportVersions for remaining Reports
    if ($reports.Count -gt $reportVersions.Count) {
        $missingCount = $reports.Count - $reportVersions.Count
        Write-Host "Generating $missingCount new ReportVersions for remaining reports..." -ForegroundColor Cyan
        
        for ($i = $existingVersionsCount; $i -lt $reports.Count; $i++) {
            $report = $reports[$i]
            
            # Create new ReportVersion based on the pattern from existing versions
            $newReportVersion = @{
                id = "v1000000-0000-0000-0000-" + ([string]($i + 1)).PadLeft(12, '0')
                reportId = $report.id
                versionNumber = 1
                description = "Initial version"
                isCurrent = $true
                reportData = "{`"title`": `"$($report.name)`", `"summary`": `"Generated report data`", `"sections`": [`"overview`", `"analysis`", `"conclusion`"]}"
                componentData = "{`"layout`": `"standard`", `"theme`": `"default`"}"
                createdBy = "00000000-0000-0000-0000-000000000001"
                creationTime = $report.CreationTime
                lastModificationTime = $report.CreationTime
            }
            
            Write-Host "  Generated new ReportVersion for Report: $($report.name) (ID: $($report.id))" -ForegroundColor Green
            $updatedReportVersions += $newReportVersion
        }
    }
    
    # Convert back to JSON and save
    $newJsonContent = $updatedReportVersions | ConvertTo-Json -Depth 10
    Set-Content -Path $ReportVersionsJsonPath -Value $newJsonContent -Encoding UTF8
    
    Write-Host "`nUpdated $ReportVersionsJsonPath with $($updatedReportVersions.Count) ReportVersions" -ForegroundColor Green
    
    # Verify the fix
    Write-Host "`nVerifying foreign key relationships..." -ForegroundColor Yellow
    $verifyContent = Get-Content $ReportVersionsJsonPath -Raw
    $verifyVersions = $verifyContent | ConvertFrom-Json
    
    $allValid = $true
    $reportIds = $reports | ForEach-Object { $_.id }
    
    foreach ($version in $verifyVersions) {
        if ($reportIds -notcontains $version.reportId) {
            Write-Host "ERROR: ReportVersion $($version.id) references non-existent Report $($version.reportId)" -ForegroundColor Red
            $allValid = $false
        }
    }
    
    if ($allValid) {
        Write-Host "Verification successful! All ReportVersions now reference valid Reports." -ForegroundColor Green
        Write-Host "Summary:" -ForegroundColor Cyan
        Write-Host "  - Total Reports: $($reports.Count)" -ForegroundColor White
        Write-Host "  - Total ReportVersions: $($updatedReportVersions.Count)" -ForegroundColor White
        Write-Host "  - Updated existing: $existingVersionsCount" -ForegroundColor White
        Write-Host "  - Generated new: $($updatedReportVersions.Count - $existingVersionsCount)" -ForegroundColor White
    }
    else {
        Write-Host "Verification failed! Some foreign key issues remain." -ForegroundColor Red
        exit 1
    }
    
    Write-Host "`nReportVersions foreign key fix completed successfully!" -ForegroundColor Green
    Write-Host "Backup saved as: $backupPath" -ForegroundColor Yellow
}
catch {
    Write-Error "Error processing JSON files: $($_.Exception.Message)"
    Write-Host "Restoring from backup..." -ForegroundColor Yellow
    Copy-Item $backupPath $ReportVersionsJsonPath -Force
    exit 1
}
