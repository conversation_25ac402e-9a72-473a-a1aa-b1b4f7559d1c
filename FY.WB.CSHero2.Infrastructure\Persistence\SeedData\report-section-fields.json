﻿[
    {
        "id":  "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa",
        "sectionId":  "11111111-1111-1111-1111-111111111111",
        "name":  "heading",
        "type":  "string",
        "content":  "Executive Summary",
        "order":  0,
        "creationTime":  "2024-03-07T08:30:00Z",
        "lastModificationTime":  "2024-03-07T08:30:00Z"
    },
    {
        "id":  "bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb",
        "sectionId":  "11111111-1111-1111-1111-111111111111",
        "name":  "content",
        "type":  "richtext",
        "content":  "This quarterly financial analysis provides a comprehensive overview of BioTech Innovations\u0027 performance for Q4 2024. Key highlights include revenue growth of 18% year-over-year, improved operational efficiency, and successful product launches in the biotechnology sector.",
        "order":  1,
        "creationTime":  "2024-03-07T08:30:00Z",
        "lastModificationTime":  "2024-03-07T08:30:00Z"
    },
    {
        "id":  "cccccccc-cccc-cccc-cccc-cccccccccccc",
        "sectionId":  "22222222-2222-2222-2222-222222222222",
        "name":  "chartType",
        "type":  "string",
        "content":  "bar",
        "order":  0,
        "creationTime":  "2024-03-07T08:30:00Z",
        "lastModificationTime":  "2024-03-07T08:30:00Z"
    },
    {
        "id":  "dddddddd-dddd-dddd-dddd-dddddddddddd",
        "sectionId":  "22222222-2222-2222-2222-222222222222",
        "name":  "data",
        "type":  "json",
        "content":  "{\"labels\": [\"Q1 2024\", \"Q2 2024\", \"Q3 2024\", \"Q4 2024\"], \"values\": [2100000, 2350000, 2600000, 2850000], \"currency\": \"USD\"}",
        "order":  1,
        "creationTime":  "2024-03-07T08:30:00Z",
        "lastModificationTime":  "2024-03-07T08:30:00Z"
    },
    {
        "id":  "eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee",
        "sectionId":  "33333333-3333-3333-3333-333333333333",
        "name":  "headers",
        "type":  "json",
        "content":  "[\"Metric\", \"Q3 2024\", \"Q4 2024\", \"Change\", \"Target\"]",
        "order":  0,
        "creationTime":  "2024-03-07T08:30:00Z",
        "lastModificationTime":  "2024-03-07T08:30:00Z"
    },
    {
        "id":  "ffffffff-ffff-ffff-ffff-ffffffffffff",
        "sectionId":  "33333333-3333-3333-3333-333333333333",
        "name":  "rows",
        "type":  "json",
        "content":  "[[\"Revenue\", \"$2.6M\", \"$2.85M\", \"+9.6%\", \"$2.8M\"], [\"Profit Margin\", \"22.5%\", \"24.8%\", \"+2.3%\", \"23%\"], [\"R\u0026D Investment\", \"$520K\", \"$570K\", \"+9.6%\", \"$550K\"], [\"Customer Count\", \"1,850\", \"2,120\", \"+14.6%\", \"2,000\"]]",
        "order":  1,
        "creationTime":  "2024-03-07T08:30:00Z",
        "lastModificationTime":  "2024-03-07T08:30:00Z"
    },
    {
        "id":  "10101010-1010-1010-1010-101010101010",
        "sectionId":  "44444444-4444-4444-4444-444444444444",
        "name":  "heading",
        "type":  "string",
        "content":  "Research \u0026 Development Progress",
        "order":  0,
        "creationTime":  "2024-03-08T09:15:00Z",
        "lastModificationTime":  "2024-03-08T09:15:00Z"
    },
    {
        "id":  "20202020-2020-2020-2020-202020202020",
        "sectionId":  "44444444-4444-4444-4444-444444444444",
        "name":  "description",
        "type":  "richtext",
        "content":  "This project focuses on advancing pharmaceutical research through innovative drug discovery methodologies. Our team has made significant progress in developing novel therapeutic compounds with enhanced efficacy and reduced side effects.",
        "order":  1,
        "creationTime":  "2024-03-08T09:15:00Z",
        "lastModificationTime":  "2024-03-08T09:15:00Z"
    },
    {
        "id":  "30303030-3030-3030-3030-303030303030",
        "sectionId":  "55555555-5555-5555-5555-555555555555",
        "name":  "milestones",
        "type":  "json",
        "content":  "[{\"date\": \"2024-01-15\", \"title\": \"Research Initiation\", \"status\": \"completed\", \"description\": \"Project kickoff and team assembly\"}, {\"date\": \"2024-04-30\", \"title\": \"Phase I Trials\", \"status\": \"completed\", \"description\": \"Initial safety testing completed\"}, {\"date\": \"2024-08-15\", \"title\": \"Phase II Trials\", \"status\": \"in-progress\", \"description\": \"Efficacy testing ongoing\"}, {\"date\": \"2024-12-31\", \"title\": \"Phase III Preparation\", \"status\": \"planned\", \"description\": \"Large-scale trial preparation\"}]",
        "order":  0,
        "creationTime":  "2024-03-08T09:15:00Z",
        "lastModificationTime":  "2024-03-08T09:15:00Z"
    },
    {
        "id":  "40404040-4040-4040-4040-404040404040",
        "sectionId":  "66666666-6666-6666-6666-666666666666",
        "name":  "heading",
        "type":  "string",
        "content":  "Service Level Agreement Overview",
        "order":  0,
        "creationTime":  "2024-03-01T10:00:00Z",
        "lastModificationTime":  "2024-03-01T10:00:00Z"
    },
    {
        "id":  "50505050-5050-5050-5050-505050505050",
        "sectionId":  "66666666-6666-6666-6666-666666666666",
        "name":  "summary",
        "type":  "richtext",
        "content":  "Our comprehensive SLA compliance analysis for Tech Startup Inc demonstrates exceptional performance across all key metrics. The organization has consistently exceeded baseline requirements while maintaining high customer satisfaction levels.",
        "order":  1,
        "creationTime":  "2024-03-01T10:00:00Z",
        "lastModificationTime":  "2024-03-01T10:00:00Z"
    },
    {
        "id":  "60606060-6060-6060-6060-606060606060",
        "sectionId":  "77777777-7777-7777-7777-777777777777",
        "name":  "chartType",
        "type":  "string",
        "content":  "line",
        "order":  0,
        "creationTime":  "2024-03-01T10:00:00Z",
        "lastModificationTime":  "2024-03-01T10:00:00Z"
    },
    {
        "id":  "70707070-7070-7070-7070-707070707070",
        "sectionId":  "77777777-7777-7777-7777-777777777777",
        "name":  "data",
        "type":  "json",
        "content":  "{\"labels\": [\"Jan\", \"Feb\", \"Mar\", \"Apr\", \"May\", \"Jun\"], \"datasets\": [{\"label\": \"Uptime %\", \"data\": [99.8, 99.9, 99.7, 99.9, 99.8, 99.9]}, {\"label\": \"Response Time (ms)\", \"data\": [120, 115, 125, 110, 118, 112]}]}",
        "order":  1,
        "creationTime":  "2024-03-01T10:00:00Z",
        "lastModificationTime":  "2024-03-01T10:00:00Z"
    },
    {
        "id":  "80808080-8080-8080-8080-808080808080",
        "sectionId":  "88888888-8888-8888-8888-888888888888",
        "name":  "headers",
        "type":  "json",
        "content":  "[\"KPI\", \"Target\", \"Actual\", \"Status\", \"Trend\"]",
        "order":  0,
        "creationTime":  "2024-03-01T10:00:00Z",
        "lastModificationTime":  "2024-03-01T10:00:00Z"
    },
    {
        "id":  "90909090-9090-9090-9090-909090909090",
        "sectionId":  "88888888-8888-8888-8888-888888888888",
        "name":  "rows",
        "type":  "json",
        "content":  "[[\"System Uptime\", \"99.5%\", \"99.8%\", \"âœ“ Exceeded\", \"â†—\"], [\"Response Time\", \"\u003c150ms\", \"116ms\", \"âœ“ Met\", \"â†—\"], [\"Error Rate\", \"\u003c0.1%\", \"0.05%\", \"âœ“ Exceeded\", \"â†˜\"], [\"Customer Satisfaction\", \"\u003e4.5/5\", \"4.7/5\", \"âœ“ Exceeded\", \"â†—\"]]",
        "order":  1,
        "creationTime":  "2024-03-01T10:00:00Z",
        "lastModificationTime":  "2024-03-01T10:00:00Z"
    },
    {
        "id":  "a0a0a0a0-a0a0-a0a0-a0a0-a0a0a0a0a0a0",
        "sectionId":  "99999999-9999-9999-9999-999999999999",
        "name":  "heading",
        "type":  "string",
        "content":  "Strategic Recommendations",
        "order":  0,
        "creationTime":  "2024-03-01T10:00:00Z",
        "lastModificationTime":  "2024-03-01T10:00:00Z"
    },
    {
        "id":  "b0b0b0b0-b0b0-b0b0-b0b0-b0b0b0b0b0b0",
        "sectionId":  "99999999-9999-9999-9999-999999999999",
        "name":  "recommendations",
        "type":  "richtext",
        "content":  "Based on our analysis, we recommend: 1) Implementing automated monitoring systems to maintain current performance levels, 2) Investing in redundant infrastructure to further improve uptime, 3) Developing proactive maintenance schedules to prevent potential issues.",
        "order":  1,
        "creationTime":  "2024-03-01T10:00:00Z",
        "lastModificationTime":  "2024-03-01T10:00:00Z"
    },
    {
        "id":  "c0c0c0c0-c0c0-c0c0-c0c0-c0c0c0c0c0c0",
        "sectionId":  "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaab",
        "name":  "headers",
        "type":  "json",
        "content":  "[\"Action Item\", \"Owner\", \"Priority\", \"Due Date\", \"Status\"]",
        "order":  0,
        "creationTime":  "2024-03-01T10:00:00Z",
        "lastModificationTime":  "2024-03-01T10:00:00Z"
    },
    {
        "id":  "d0d0d0d0-d0d0-d0d0-d0d0-d0d0d0d0d0d0",
        "sectionId":  "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaab",
        "name":  "rows",
        "type":  "json",
        "content":  "[[\"Deploy monitoring dashboard\", \"IT Team\", \"High\", \"2024-04-15\", \"In Progress\"], [\"Update SLA documentation\", \"Operations\", \"Medium\", \"2024-04-30\", \"Pending\"], [\"Conduct quarterly review\", \"Management\", \"High\", \"2024-06-30\", \"Scheduled\"]]",
        "order":  1,
        "creationTime":  "2024-03-01T10:00:00Z",
        "lastModificationTime":  "2024-03-01T10:00:00Z"
    },
    {
        "id":  "e0e0e0e0-e0e0-e0e0-e0e0-e0e0e0e0e0e0",
        "sectionId":  "bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbc",
        "name":  "heading",
        "type":  "string",
        "content":  "Product Usage Analysis",
        "order":  0,
        "creationTime":  "2024-03-01T10:30:00Z",
        "lastModificationTime":  "2024-03-01T10:30:00Z"
    },
    {
        "id":  "f0f0f0f0-f0f0-f0f0-f0f0-f0f0f0f0f0f0",
        "sectionId":  "bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbc",
        "name":  "overview",
        "type":  "richtext",
        "content":  "CloudTech Solutions has demonstrated strong product adoption across their cloud infrastructure platform. User engagement metrics show consistent growth with particular strength in enterprise features and API utilization.",
        "order":  1,
        "creationTime":  "2024-03-01T10:30:00Z",
        "lastModificationTime":  "2024-03-01T10:30:00Z"
    },
    {
        "id":  "01010101-0101-0101-0101-010101010101",
        "sectionId":  "cccccccc-cccc-cccc-cccc-ccccccccccccd",
        "name":  "chartType",
        "type":  "string",
        "content":  "area",
        "order":  0,
        "creationTime":  "2024-03-01T10:30:00Z",
        "lastModificationTime":  "2024-03-01T10:30:00Z"
    },
    {
        "id":  "02020202-0202-0202-0202-020202020202",
        "sectionId":  "cccccccc-cccc-cccc-cccc-ccccccccccccd",
        "name":  "data",
        "type":  "json",
        "content":  "{\"labels\": [\"Week 1\", \"Week 2\", \"Week 3\", \"Week 4\"], \"datasets\": [{\"label\": \"New Users\", \"data\": [45, 52, 48, 61]}, {\"label\": \"Active Users\", \"data\": [320, 345, 378, 402]}, {\"label\": \"API Calls\", \"data\": [12500, 14200, 15800, 17300]}]}",
        "order":  1,
        "creationTime":  "2024-03-01T10:30:00Z",
        "lastModificationTime":  "2024-03-01T10:30:00Z"
    },
    {
        "id":  "03030303-0303-0303-0303-030303030303",
        "sectionId":  "dddddddd-dddd-dddd-dddd-ddddddddddde",
        "name":  "headers",
        "type":  "json",
        "content":  "[\"Metric\", \"Current Month\", \"Previous Month\", \"Change\", \"Benchmark\"]",
        "order":  0,
        "creationTime":  "2024-03-01T10:30:00Z",
        "lastModificationTime":  "2024-03-01T10:30:00Z"
    },
    {
        "id":  "04040404-0404-0404-0404-040404040404",
        "sectionId":  "dddddddd-dddd-dddd-dddd-ddddddddddde",
        "name":  "rows",
        "type":  "json",
        "content":  "[[\"Daily Active Users\", \"402\", \"378\", \"+6.3%\", \"350\"], [\"Session Duration\", \"24.5 min\", \"22.1 min\", \"+10.9%\", \"20 min\"], [\"Feature Adoption\", \"78%\", \"72%\", \"+8.3%\", \"70%\"], [\"User Retention\", \"85%\", \"82%\", \"+3.7%\", \"80%\"]]",
        "order":  1,
        "creationTime":  "2024-03-01T10:30:00Z",
        "lastModificationTime":  "2024-03-01T10:30:00Z"
    },
    {
        "id":  "05050505-0505-0505-0505-050505050505",
        "sectionId":  "eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeef",
        "name":  "chartType",
        "type":  "string",
        "content":  "doughnut",
        "order":  0,
        "creationTime":  "2024-03-01T10:30:00Z",
        "lastModificationTime":  "2024-03-01T10:30:00Z"
    },
    {
        "id":  "06060606-0606-0606-0606-060606060606",
        "sectionId":  "eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeef",
        "name":  "data",
        "type":  "json",
        "content":  "{\"labels\": [\"Storage API\", \"Compute API\", \"Database API\", \"Analytics API\", \"Security API\"], \"data\": [35, 28, 20, 12, 5], \"colors\": [\"#FF6384\", \"#36A2EB\", \"#FFCE56\", \"#4BC0C0\", \"#9966FF\"]}",
        "order":  1,
        "creationTime":  "2024-03-01T10:30:00Z",
        "lastModificationTime":  "2024-03-01T10:30:00Z"
    },
    {
        "id":  "07070707-0707-0707-0707-070707070707",
        "sectionId":  "ffffffff-ffff-ffff-ffff-fffffffffff0",
        "name":  "heading",
        "type":  "string",
        "content":  "Growth Opportunities",
        "order":  0,
        "creationTime":  "2024-03-01T10:30:00Z",
        "lastModificationTime":  "2024-03-01T10:30:00Z"
    },
    {
        "id":  "08080808-0808-0808-0808-080808080808",
        "sectionId":  "ffffffff-ffff-ffff-ffff-fffffffffff0",
        "name":  "opportunities",
        "type":  "richtext",
        "content":  "Key growth opportunities include: expanding enterprise features, developing mobile applications, enhancing API documentation, and implementing advanced analytics capabilities. Market research indicates strong demand for these enhancements.",
        "order":  1,
        "creationTime":  "2024-03-01T10:30:00Z",
        "lastModificationTime":  "2024-03-01T10:30:00Z"
    },
    {
        "id":  "09090909-0909-0909-0909-090909090909",
        "sectionId":  "10101010-1010-1010-1010-101010101011",
        "name":  "heading",
        "type":  "string",
        "content":  "AI Implementation Summary",
        "order":  0,
        "creationTime":  "2024-03-02T11:00:00Z",
        "lastModificationTime":  "2024-03-02T11:00:00Z"
    },
    {
        "id":  "0a0a0a0a-0a0a-0a0a-0a0a-0a0a0a0a0a0a",
        "sectionId":  "10101010-1010-1010-1010-101010101011",
        "name":  "summary",
        "type":  "richtext",
        "content":  "AI Research Labs has successfully implemented cutting-edge artificial intelligence solutions across multiple domains. Our machine learning models demonstrate superior performance in natural language processing, computer vision, and predictive analytics applications.",
        "order":  1,
        "creationTime":  "2024-03-02T11:00:00Z",
        "lastModificationTime":  "2024-03-02T11:00:00Z"
    },
    {
        "id":  "0b0b0b0b-0b0b-0b0b-0b0b-0b0b0b0b0b0b",
        "sectionId":  "20202020-2020-2020-2020-202020202022",
        "name":  "chartType",
        "type":  "string",
        "content":  "radar",
        "order":  0,
        "creationTime":  "2024-03-02T11:00:00Z",
        "lastModificationTime":  "2024-03-02T11:00:00Z"
    },
    {
        "id":  "0c0c0c0c-0c0c-0c0c-0c0c-0c0c0c0c0c0c",
        "sectionId":  "20202020-2020-2020-2020-202020202022",
        "name":  "data",
        "type":  "json",
        "content":  "{\"labels\": [\"Accuracy\", \"Speed\", \"Scalability\", \"Reliability\", \"Cost Efficiency\"], \"datasets\": [{\"label\": \"Current Performance\", \"data\": [92, 88, 85, 94, 78]}, {\"label\": \"Industry Benchmark\", \"data\": [85, 80, 75, 88, 70]}]}",
        "order":  1,
        "creationTime":  "2024-03-02T11:00:00Z",
        "lastModificationTime":  "2024-03-02T11:00:00Z"
    },
    {
        "id":  "0d0d0d0d-0d0d-0d0d-0d0d-0d0d0d0d0d0d",
        "sectionId":  "30303030-3030-3030-3030-303030303033",
        "name":  "headers",
        "type":  "json",
        "content":  "[\"Model Type\", \"Accuracy\", \"Training Time\", \"Inference Speed\", \"Use Case\"]",
        "order":  0,
        "creationTime":  "2024-03-02T11:00:00Z",
        "lastModificationTime":  "2024-03-02T11:00:00Z"
    },
    {
        "id":  "0e0e0e0e-0e0e-0e0e-0e0e-0e0e0e0e0e0e",
        "sectionId":  "30303030-3030-3030-3030-303030303033",
        "name":  "rows",
        "type":  "json",
        "content":  "[[\"NLP Transformer\", \"94.2%\", \"12 hours\", \"15ms\", \"Text Analysis\"], [\"CNN Vision\", \"96.8%\", \"8 hours\", \"8ms\", \"Image Recognition\"], [\"LSTM Predictor\", \"89.5%\", \"6 hours\", \"12ms\", \"Time Series\"], [\"Random Forest\", \"87.3%\", \"2 hours\", \"5ms\", \"Classification\"]]",
        "order":  1,
        "creationTime":  "2024-03-02T11:00:00Z",
        "lastModificationTime":  "2024-03-02T11:00:00Z"
    },
    {
        "id":  "0f0f0f0f-0f0f-0f0f-0f0f-0f0f0f0f0f0f",
        "sectionId":  "40404040-4040-4040-4040-404040404044",
        "name":  "chartType",
        "type":  "bar",
        "order":  0,
        "creationTime":  "2024-03-02T11:00:00Z",
        "lastModificationTime":  "2024-03-02T11:00:00Z"
    },
    {
        "id":  "10101010-1010-1010-1010-101010101011",
        "sectionId":  "40404040-4040-4040-4040-404040404044",
        "name":  "data",
        "type":  "json",
        "content":  "{\"labels\": [\"Year 1\", \"Year 2\", \"Year 3\", \"Projected\"], \"datasets\": [{\"label\": \"Investment ($M)\", \"data\": [2.5, 3.2, 4.1, 5.8]}, {\"label\": \"Revenue ($M)\", \"data\": [1.8, 4.2, 7.5, 12.3]}, {\"label\": \"ROI (%)\", \"data\": [-28, 31, 83, 112]}]}",
        "order":  1,
        "creationTime":  "2024-03-02T11:00:00Z",
        "lastModificationTime":  "2024-03-02T11:00:00Z"
    },
    {
        "id":  "11111111-1111-1111-1111-111111111112",
        "sectionId":  "50505050-5050-5050-5050-505050505055",
        "name":  "milestones",
        "type":  "json",
        "content":  "[{\"date\": \"2024-06-01\", \"title\": \"Model Optimization\", \"status\": \"planned\", \"description\": \"Enhance existing models for better performance\"}, {\"date\": \"2024-09-01\", \"title\": \"New AI Platform\", \"status\": \"planned\", \"description\": \"Launch next-generation AI platform\"}, {\"date\": \"2024-12-01\", \"title\": \"Enterprise Integration\", \"status\": \"planned\", \"description\": \"Full enterprise solution deployment\"}]",
        "order":  0,
        "creationTime":  "2024-03-02T11:00:00Z",
        "lastModificationTime":  "2024-03-02T11:00:00Z"
    },
    {
        "id":  "12121212-1212-1212-1212-121212121213",
        "sectionId":  "60606060-6060-6060-6060-606060606066",
        "name":  "heading",
        "type":  "string",
        "content":  "Patient Feedback Analysis",
        "order":  0,
        "creationTime":  "2024-03-03T14:15:00Z",
        "lastModificationTime":  "2024-03-03T14:15:00Z"
    },
    {
        "id":  "13131313-1313-1313-1313-131313131314",
        "sectionId":  "60606060-6060-6060-6060-606060606066",
        "name":  "overview",
        "type":  "richtext",
        "content":  "MedClinic Center\u0027s patient feedback analysis reveals consistently high satisfaction scores across all service areas. Patients particularly appreciate the quality of care, staff professionalism, and facility cleanliness. Areas for improvement include wait times and appointment scheduling.",
        "order":  1,
        "creationTime":  "2024-03-03T14:15:00Z",
        "lastModificationTime":  "2024-03-03T14:15:00Z"
    },
    {
        "id":  "14141414-1414-1414-1414-141414141415",
        "sectionId":  "70707070-7070-7070-7070-707070707077",
        "name":  "chartType",
        "type":  "string",
        "content":  "horizontalBar",
        "order":  0,
        "creationTime":  "2024-03-03T14:15:00Z",
        "lastModificationTime":  "2024-03-03T14:15:00Z"
    },
    {
        "id":  "15151515-1515-1515-1515-151515151516",
        "sectionId":  "70707070-7070-7070-7070-707070707077",
        "name":  "data",
        "type":  "json",
        "content":  "{\"labels\": [\"Overall Experience\", \"Quality of Care\", \"Staff Courtesy\", \"Facility Cleanliness\", \"Wait Times\", \"Communication\"], \"data\": [4.6, 4.8, 4.7, 4.9, 3.8, 4.5], \"maxScore\": 5}",
        "order":  1,
        "creationTime":  "2024-03-03T14:15:00Z",
        "lastModificationTime":  "2024-03-03T14:15:00Z"
    },
    {
        "id":  "16161616-1616-1616-1616-161616161617",
        "sectionId":  "80808080-8080-8080-8080-808080808088",
        "name":  "headers",
        "type":  "json",
        "content":  "[\"Service Area\", \"Current Score\", \"Target Score\", \"Improvement\", \"Priority\"]",
        "order":  0,
        "creationTime":  "2024-03-03T14:15:00Z",
        "lastModificationTime":  "2024-03-03T14:15:00Z"
    },
    {
        "id":  "17171717-1717-1717-1717-171717171718",
        "sectionId":  "80808080-8080-8080-8080-808080808088",
        "name":  "rows",
        "type":  "json",
        "content":  "[[\"Emergency Care\", \"4.5\", \"4.7\", \"+4.4%\", \"Medium\"], [\"Outpatient Services\", \"4.6\", \"4.8\", \"+4.3%\", \"Medium\"], [\"Wait Times\", \"3.8\", \"4.2\", \"+10.5%\", \"High\"], [\"Appointment Scheduling\", \"4.1\", \"4.5\", \"+9.8%\", \"High\"]]",
        "order":  1,
        "creationTime":  "2024-03-03T14:15:00Z",
        "lastModificationTime":  "2024-03-03T14:15:00Z"
    }
]
