# Final Summary of Foreign Key Status
$ErrorActionPreference = "Stop"

$reportsPath = "FY.WB.CSHero2.Infrastructure/Persistence/SeedData/reports.json"
$reportVersionsPath = "FY.WB.CSHero2.Infrastructure/Persistence/SeedData/report-versions.json"

Write-Host "=== FOREIGN KEY MISMATCH RESOLUTION SUMMARY ===" -ForegroundColor Cyan
Write-Host "Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray

# Load data
$reports = Get-Content $reportsPath -Raw | ConvertFrom-Json
$reportVersions = Get-Content $reportVersionsPath -Raw | ConvertFrom-Json

Write-Host "`nDATA COUNTS:" -ForegroundColor Yellow
Write-Host "  Reports: $($reports.Count)" -ForegroundColor Green
Write-Host "  ReportVersions: $($reportVersions.Count)" -ForegroundColor Green

# Verify all foreign keys
$validReportIds = @{}
foreach ($report in $reports) {
    $validReportIds[$report.id] = $true
}

$validCount = 0
$invalidCount = 0

for ($i = 0; $i -lt $reportVersions.Count; $i++) {
    $version = $reportVersions[$i]
    $reportId = $version.reportId
    
    if ($validReportIds.ContainsKey($reportId)) {
        $validCount++
    } else {
        $invalidCount++
    }
}

Write-Host "`nFOREIGN KEY VALIDATION:" -ForegroundColor Yellow
Write-Host "  Valid References: $validCount" -ForegroundColor Green
Write-Host "  Invalid References: $invalidCount" -ForegroundColor $(if ($invalidCount -eq 0) { "Green" } else { "Red" })

if ($invalidCount -eq 0) {
    Write-Host "`n✅ RESOLUTION STATUS: COMPLETE" -ForegroundColor Green
    Write-Host "All ReportVersions now correctly reference existing Reports." -ForegroundColor Green
    Write-Host "The foreign key mismatch issue has been resolved." -ForegroundColor Green
} else {
    Write-Host "`n❌ RESOLUTION STATUS: INCOMPLETE" -ForegroundColor Red
    Write-Host "Foreign key mismatches still exist and need attention." -ForegroundColor Red
}

Write-Host "`nRECOMMENDATIONS:" -ForegroundColor Yellow
if ($invalidCount -eq 0) {
    Write-Host "  • No further action required" -ForegroundColor Green
    Write-Host "  • Data integrity is maintained" -ForegroundColor Green
    Write-Host "  • Ready for production use" -ForegroundColor Green
} else {
    Write-Host "  • Run fix-report-versions-fk.ps1 to correct mismatches" -ForegroundColor Red
    Write-Host "  • Verify data after fixes" -ForegroundColor Red
}
