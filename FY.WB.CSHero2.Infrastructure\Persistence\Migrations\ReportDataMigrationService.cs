using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using System.Threading;
using FY.WB.CSHero2.Domain.Entities;
using FY.WB.CSHero2.Application.Services.Migration;
using FY.WB.CSHero2.Application.Models.Migration;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace FY.WB.CSHero2.Infrastructure.Persistence.Migrations
{
    /// <summary>
    /// Service for migrating existing report JSON data to multi-storage architecture
    /// </summary>
    public class ReportDataMigrationService : IReportDataMigrationService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<ReportDataMigrationService> _logger;

        public ReportDataMigrationService(
            ApplicationDbContext context,
            ILogger<ReportDataMigrationService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Migrates all existing report data from JSON format to structured entities
        /// </summary>
        public async Task<int> MigrateReportDataAsync()
        {
            _logger.LogInformation("Starting report data migration...");

            // Get all reports with their current versions
            var reports = await _context.Reports
                .Include(r => r.Versions.Where(v => v.IsCurrent))
                .Where(r => !r.IsDeleted)
                .ToListAsync();

            _logger.LogInformation("Found {ReportCount} reports to migrate", reports.Count);

            int migratedCount = 0;
            int skippedCount = 0;

            foreach (var report in reports)
            {
                try
                {
                    var currentVersion = report.Versions.FirstOrDefault(v => v.IsCurrent);
                    if (currentVersion == null)
                    {
                        _logger.LogWarning("Report {ReportId} has no current version, skipping", report.Id);
                        skippedCount++;
                        continue;
                    }

                    // Check if report already has sections (already migrated)
                    var existingSections = await _context.ReportSections
                        .Where(s => s.ReportId == report.Id)
                        .CountAsync();

                    if (existingSections > 0)
                    {
                        _logger.LogInformation("Report {ReportId} already has {SectionCount} sections, skipping", 
                            report.Id, existingSections);
                        skippedCount++;
                        continue;
                    }

                    // Migrate the report data
                    var migrated = await MigrateReportAsync(report, currentVersion);
                    if (migrated)
                    {
                        migratedCount++;
                    }
                    else
                    {
                        skippedCount++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error migrating report {ReportId}: {Message}", report.Id, ex.Message);
                    skippedCount++;
                }
            }

            await _context.SaveChangesAsync();
            _logger.LogInformation("Completed report data migration. Migrated: {MigratedCount}, Skipped: {SkippedCount}, Total: {TotalCount}",
                migratedCount, skippedCount, reports.Count);

            return migratedCount;
        }

        /// <summary>
        /// Migrates a single report's data
        /// </summary>
        private async Task<bool> MigrateReportAsync(Report report, ReportVersion version)
        {
            var jsonData = version.JsonData;
            if (string.IsNullOrEmpty(jsonData))
            {
                _logger.LogWarning("Report {ReportId} has empty JSON data, creating default section", report.Id);
                await CreateDefaultSectionAsync(report);
                return true;
            }

            try
            {
                // Try to parse as structured format first
                var structuredData = JsonSerializer.Deserialize<ReportContentModel>(jsonData,
                    new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                if (structuredData?.Sections != null && structuredData.Sections.Any())
                {
                    return await MigrateStructuredDataAsync(report, structuredData);
                }

                // Try to parse as flat format
                var flatData = JsonSerializer.Deserialize<Dictionary<string, object>>(jsonData);
                if (flatData != null && flatData.Any())
                {
                    return await MigrateFlatDataAsync(report, flatData);
                }

                _logger.LogWarning("Report {ReportId} has unrecognized JSON format, creating default section", report.Id);
                await CreateDefaultSectionAsync(report);
                return true;
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "Failed to parse JSON for report {ReportId}, creating default section", report.Id);
                await CreateDefaultSectionAsync(report);
                return true;
            }
        }

        /// <summary>
        /// Migrates structured JSON data (with sections array)
        /// </summary>
        private async Task<bool> MigrateStructuredDataAsync(Report report, ReportContentModel reportContent)
        {
            _logger.LogDebug("Migrating structured data for report {ReportId} with {SectionCount} sections",
                report.Id, reportContent.Sections!.Count);

            int sectionOrder = 0;
            foreach (var sectionData in reportContent.Sections!)
            {
                var section = new ReportSection(
                    Guid.NewGuid(),
                    report.Id,
                    sectionData.SectionTitle ?? "Untitled Section",
                    sectionData.Type ?? "text",
                    sectionOrder++);

                _context.ReportSections.Add(section);

                // Process section content
                if (sectionData.Content != null)
                {
                    await ProcessSectionContentAsync(section, sectionData.Content);
                }
            }

            return true;
        }

        /// <summary>
        /// Migrates flat JSON data (key-value pairs)
        /// </summary>
        private async Task<bool> MigrateFlatDataAsync(Report report, Dictionary<string, object> flatData)
        {
            _logger.LogDebug("Migrating flat data for report {ReportId} with {FieldCount} fields",
                report.Id, flatData.Count);

            // Group fields by section prefix (e.g., "header.title" -> "header" section)
            var sectionGroups = GroupFieldsBySection(flatData);

            int sectionOrder = 0;
            foreach (var sectionGroup in sectionGroups)
            {
                var section = new ReportSection(
                    Guid.NewGuid(),
                    report.Id,
                    CapitalizeFirstLetter(sectionGroup.Key),
                    DetermineSectionType(sectionGroup.Value),
                    sectionOrder++);

                _context.ReportSections.Add(section);

                // Create fields for this section
                int fieldOrder = 0;
                foreach (var field in sectionGroup.Value)
                {
                    var fieldName = field.Key.Contains('.') ? field.Key.Split('.').Last() : field.Key;
                    var sectionField = new ReportSectionField(
                        Guid.NewGuid(),
                        section.Id,
                        fieldName,
                        DetermineFieldType(field.Value),
                        field.Value?.ToString() ?? string.Empty,
                        fieldOrder++);

                    _context.ReportSectionFields.Add(sectionField);
                }
            }

            return true;
        }

        /// <summary>
        /// Processes content from a structured section
        /// </summary>
        private Task ProcessSectionContentAsync(ReportSection section, object content)
        {
            var contentDict = ExtractFieldsFromContent(content);
            
            int fieldOrder = 0;
            foreach (var field in contentDict)
            {
                var sectionField = new ReportSectionField(
                    Guid.NewGuid(),
                    section.Id,
                    field.Key,
                    DetermineFieldType(field.Value),
                    field.Value?.ToString() ?? string.Empty,
                    fieldOrder++);

                _context.ReportSectionFields.Add(sectionField);
            }

            return Task.CompletedTask;
        }

        /// <summary>
        /// Creates a default section for reports with no data
        /// </summary>
        private Task CreateDefaultSectionAsync(Report report)
        {
            var section = new ReportSection(
                Guid.NewGuid(),
                report.Id,
                "Content",
                "text",
                0);

            _context.ReportSections.Add(section);

            var field = new ReportSectionField(
                Guid.NewGuid(),
                section.Id,
                "content",
                "string",
                "No content available",
                0);

            _context.ReportSectionFields.Add(field);

            return Task.CompletedTask;
        }

        /// <summary>
        /// Groups flat fields by section prefix
        /// </summary>
        private Dictionary<string, Dictionary<string, object>> GroupFieldsBySection(Dictionary<string, object> flatData)
        {
            var groups = new Dictionary<string, Dictionary<string, object>>();

            foreach (var kvp in flatData)
            {
                var sectionName = "general";
                if (kvp.Key.Contains('.'))
                {
                    sectionName = kvp.Key.Split('.')[0];
                }

                if (!groups.ContainsKey(sectionName))
                {
                    groups[sectionName] = new Dictionary<string, object>();
                }

                groups[sectionName][kvp.Key] = kvp.Value;
            }

            return groups;
        }

        /// <summary>
        /// Extracts fields from content object
        /// </summary>
        private Dictionary<string, object> ExtractFieldsFromContent(object content)
        {
            var fields = new Dictionary<string, object>();

            if (content is JsonElement jsonElement)
            {
                if (jsonElement.ValueKind == JsonValueKind.Object)
                {
                    foreach (var property in jsonElement.EnumerateObject())
                    {
                        fields[property.Name] = ExtractJsonElementValue(property.Value);
                    }
                }
                else
                {
                    fields["content"] = ExtractJsonElementValue(jsonElement);
                }
            }
            else
            {
                fields["content"] = content;
            }

            return fields;
        }

        /// <summary>
        /// Extracts value from JsonElement
        /// </summary>
        private object ExtractJsonElementValue(JsonElement element)
        {
            return element.ValueKind switch
            {
                JsonValueKind.String => element.GetString() ?? string.Empty,
                JsonValueKind.Number => element.GetDecimal(),
                JsonValueKind.True => true,
                JsonValueKind.False => false,
                JsonValueKind.Null => string.Empty,
                JsonValueKind.Object => element.GetRawText(),
                JsonValueKind.Array => element.GetRawText(),
                _ => element.GetRawText()
            };
        }

        /// <summary>
        /// Determines section type based on fields
        /// </summary>
        private string DetermineSectionType(Dictionary<string, object> fields)
        {
            var fieldNames = fields.Keys.Select(k => k.ToLower()).ToList();

            if (fieldNames.Any(f => f.Contains("chart") || f.Contains("graph")))
                return "chart";
            if (fieldNames.Any(f => f.Contains("table") || f.Contains("row") || f.Contains("column")))
                return "table";
            if (fieldNames.Any(f => f.Contains("list") || f.Contains("item")))
                return "list";
            if (fieldNames.Any(f => f.Contains("timeline") || f.Contains("date") || f.Contains("time")))
                return "timeline";

            return "text";
        }

        /// <summary>
        /// Determines field type based on value
        /// </summary>
        private string DetermineFieldType(object? value)
        {
            if (value == null) return "string";

            return value switch
            {
                bool => "boolean",
                int or long or decimal or double or float => "number",
                DateTime => "date",
                string str when DateTime.TryParse(str, out _) => "date",
                string str when decimal.TryParse(str, out _) => "number",
                string str when bool.TryParse(str, out _) => "boolean",
                string str when str.StartsWith("{") || str.StartsWith("[") => "json",
                _ => "string"
            };
        }

        /// <summary>
        /// Capitalizes the first letter of a string
        /// </summary>
        private string CapitalizeFirstLetter(string input)
        {
            if (string.IsNullOrEmpty(input)) return input;
            return char.ToUpper(input[0]) + input.Substring(1);
        }

        #region IReportDataMigrationService Implementation

        /// <summary>
        /// Migrates all reports from SQL JSON storage to multi-storage architecture
        /// </summary>
        public async Task<MigrationResult> MigrateAllReportsAsync(
            MigrationOptions? options = null,
            CancellationToken cancellationToken = default)
        {
            var migrationOptions = options ?? new MigrationOptions();
            var result = new MigrationResult
            {
                OperationId = Guid.NewGuid(),
                StartTime = DateTime.UtcNow
            };

            try
            {
                _logger.LogInformation("Starting migration of all reports. OperationId: {OperationId}", result.OperationId);

                // For now, use the existing migration logic
                var migratedCount = await MigrateReportDataAsync();

                result.Success = true;
                result.TotalReports = migratedCount;
                result.SuccessfulMigrations = migratedCount;
                result.FailedMigrations = 0;
                result.EndTime = DateTime.UtcNow;
                result.Duration = result.EndTime.Value - result.StartTime;

                _logger.LogInformation("Migration of all reports completed successfully. Migrated: {Count}", migratedCount);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.EndTime = DateTime.UtcNow;
                result.Duration = result.EndTime.Value - result.StartTime;
                result.Errors.Add(new MigrationError { Message = $"Migration failed: {ex.Message}" });

                _logger.LogError(ex, "Migration of all reports failed");
                throw;
            }

            return result;
        }

        /// <summary>
        /// Migrates a specific report and all its versions
        /// </summary>
        public async Task<MigrationResult> MigrateReportAsync(
            Guid reportId,
            MigrationOptions? options = null,
            CancellationToken cancellationToken = default)
        {
            var result = new MigrationResult
            {
                OperationId = Guid.NewGuid(),
                StartTime = DateTime.UtcNow
            };

            try
            {
                _logger.LogInformation("Starting migration of report {ReportId}. OperationId: {OperationId}",
                    reportId, result.OperationId);

                var report = await _context.Reports
                    .Include(r => r.Versions.Where(v => v.IsCurrent))
                    .FirstOrDefaultAsync(r => r.Id == reportId && !r.IsDeleted, cancellationToken);

                if (report == null)
                {
                    result.Success = false;
                    result.Errors.Add(new MigrationError { Message = $"Report {reportId} not found" });
                    return result;
                }

                var currentVersion = report.Versions.FirstOrDefault(v => v.IsCurrent);
                if (currentVersion == null)
                {
                    result.Success = false;
                    result.Errors.Add(new MigrationError { Message = $"Report {reportId} has no current version" });
                    return result;
                }

                var migrated = await MigrateReportAsync(report, currentVersion);
                await _context.SaveChangesAsync(cancellationToken);

                result.Success = migrated;
                result.TotalReports = 1;
                result.SuccessfulMigrations = migrated ? 1 : 0;
                result.FailedMigrations = migrated ? 0 : 1;
                result.EndTime = DateTime.UtcNow;
                result.Duration = result.EndTime.Value - result.StartTime;

                _logger.LogInformation("Migration of report {ReportId} completed. Success: {Success}",
                    reportId, migrated);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.EndTime = DateTime.UtcNow;
                result.Duration = result.EndTime.Value - result.StartTime;
                result.Errors.Add(new MigrationError { Message = $"Migration failed: {ex.Message}" });

                _logger.LogError(ex, "Migration of report {ReportId} failed", reportId);
                throw;
            }

            return result;
        }

        /// <summary>
        /// Migrates a specific report version
        /// </summary>
        public async Task<MigrationResult> MigrateReportVersionAsync(
            Guid reportId,
            Guid versionId,
            MigrationOptions? options = null,
            CancellationToken cancellationToken = default)
        {
            var result = new MigrationResult
            {
                OperationId = Guid.NewGuid(),
                StartTime = DateTime.UtcNow
            };

            try
            {
                _logger.LogInformation("Starting migration of report {ReportId}, version {VersionId}. OperationId: {OperationId}",
                    reportId, versionId, result.OperationId);

                var report = await _context.Reports
                    .Include(r => r.Versions.Where(v => v.Id == versionId))
                    .FirstOrDefaultAsync(r => r.Id == reportId && !r.IsDeleted, cancellationToken);

                if (report == null)
                {
                    result.Success = false;
                    result.Errors.Add(new MigrationError { Message = $"Report {reportId} not found" });
                    return result;
                }

                var version = report.Versions.FirstOrDefault(v => v.Id == versionId);
                if (version == null)
                {
                    result.Success = false;
                    result.Errors.Add(new MigrationError { Message = $"Version {versionId} not found for report {reportId}" });
                    return result;
                }

                var migrated = await MigrateReportAsync(report, version);
                await _context.SaveChangesAsync(cancellationToken);

                result.Success = migrated;
                result.TotalReports = 1;
                result.SuccessfulMigrations = migrated ? 1 : 0;
                result.FailedMigrations = migrated ? 0 : 1;
                result.EndTime = DateTime.UtcNow;
                result.Duration = result.EndTime.Value - result.StartTime;

                _logger.LogInformation("Migration of report {ReportId}, version {VersionId} completed. Success: {Success}",
                    reportId, versionId, migrated);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.EndTime = DateTime.UtcNow;
                result.Duration = result.EndTime.Value - result.StartTime;
                result.Errors.Add(new MigrationError { Message = $"Migration failed: {ex.Message}" });

                _logger.LogError(ex, "Migration of report {ReportId}, version {VersionId} failed", reportId, versionId);
                throw;
            }

            return result;
        }

        /// <summary>
        /// Gets the current migration status and progress
        /// </summary>
        public async Task<MigrationStatus> GetMigrationStatusAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                var totalReports = await _context.Reports
                    .Where(r => !r.IsDeleted)
                    .CountAsync(cancellationToken);

                var migratedReports = await _context.Reports
                    .Where(r => !r.IsDeleted && _context.ReportSections.Any(s => s.ReportId == r.Id))
                    .CountAsync(cancellationToken);

                return new MigrationStatus
                {
                    IsRunning = false,
                    CurrentOperationId = null,
                    OverallStatistics = new MigrationStatistics
                    {
                        TotalReports = totalReports,
                        MigratedReports = migratedReports,
                        PendingReports = totalReports - migratedReports
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting migration status");
                throw;
            }
        }

        /// <summary>
        /// Gets detailed migration statistics
        /// </summary>
        public async Task<MigrationStatistics> GetMigrationStatisticsAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                var totalReports = await _context.Reports.Where(r => !r.IsDeleted).CountAsync(cancellationToken);
                var migratedReports = await _context.Reports
                    .Where(r => !r.IsDeleted && _context.ReportSections.Any(s => s.ReportId == r.Id))
                    .CountAsync(cancellationToken);

                var totalSections = await _context.ReportSections.CountAsync(cancellationToken);
                var totalFields = await _context.ReportSectionFields.CountAsync(cancellationToken);

                return new MigrationStatistics
                {
                    TotalReports = totalReports,
                    MigratedReports = migratedReports,
                    PendingReports = totalReports - migratedReports,
                    LastMigrationTime = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting migration statistics");
                throw;
            }
        }

        /// <summary>
        /// Validates that a report has been successfully migrated
        /// </summary>
        public async Task<ValidationResult> ValidateMigrationAsync(
            Guid reportId,
            CancellationToken cancellationToken = default)
        {
            var result = new ValidationResult();

            try
            {
                _logger.LogInformation("Validating migration for report {ReportId}", reportId);

                var report = await _context.Reports
                    .FirstOrDefaultAsync(r => r.Id == reportId && !r.IsDeleted, cancellationToken);

                if (report == null)
                {
                    result.IsValid = false;
                    result.Errors.Add(new ValidationError
                    {
                        ErrorCode = "REPORT_NOT_FOUND",
                        Message = $"Report {reportId} not found",
                        Severity = ErrorSeverity.Error
                    });
                    return result;
                }

                var sectionsCount = await _context.ReportSections
                    .Where(s => s.ReportId == reportId)
                    .CountAsync(cancellationToken);

                if (sectionsCount == 0)
                {
                    result.IsValid = false;
                    result.Errors.Add(new ValidationError
                    {
                        ErrorCode = "NO_SECTIONS",
                        Message = $"Report {reportId} has no migrated sections",
                        Severity = ErrorSeverity.Error
                    });
                }
                else
                {
                    result.IsValid = true;
                    result.ValidationScore = 100.0;
                }

                _logger.LogInformation("Validation for report {ReportId} completed. Valid: {IsValid}",
                    reportId, result.IsValid);
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.Errors.Add(new ValidationError
                {
                    ErrorCode = "VALIDATION_ERROR",
                    Message = $"Validation failed: {ex.Message}",
                    Severity = ErrorSeverity.Error
                });

                _logger.LogError(ex, "Error validating migration for report {ReportId}", reportId);
            }

            return result;
        }

        /// <summary>
        /// Validates that a specific report version has been successfully migrated
        /// </summary>
        public async Task<ValidationResult> ValidateVersionMigrationAsync(
            Guid reportId,
            Guid versionId,
            CancellationToken cancellationToken = default)
        {
            // For now, delegate to the report validation since we're migrating current versions
            return await ValidateMigrationAsync(reportId, cancellationToken);
        }

        /// <summary>
        /// Rolls back migration for a specific report
        /// </summary>
        public async Task<RollbackResult> RollbackReportMigrationAsync(
            Guid reportId,
            CancellationToken cancellationToken = default)
        {
            var result = new RollbackResult();

            try
            {
                _logger.LogInformation("Starting rollback for report {ReportId}", reportId);

                // Remove migrated sections and fields
                var sections = await _context.ReportSections
                    .Where(s => s.ReportId == reportId)
                    .ToListAsync(cancellationToken);

                var sectionIds = sections.Select(s => s.Id).ToList();
                var fields = await _context.ReportSectionFields
                    .Where(f => sectionIds.Contains(f.SectionId))
                    .ToListAsync(cancellationToken);

                _context.ReportSectionFields.RemoveRange(fields);
                _context.ReportSections.RemoveRange(sections);

                await _context.SaveChangesAsync(cancellationToken);

                result.Success = true;
                result.ReportsRolledBack = 1;
                result.Duration = TimeSpan.FromSeconds(1); // Placeholder

                _logger.LogInformation("Rollback for report {ReportId} completed successfully", reportId);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Duration = TimeSpan.FromSeconds(1); // Placeholder
                result.Errors.Add($"Rollback failed: {ex.Message}");

                _logger.LogError(ex, "Rollback for report {ReportId} failed", reportId);
                throw;
            }

            return result;
        }

        /// <summary>
        /// Rolls back migration for a specific report version
        /// </summary>
        public async Task<RollbackResult> RollbackVersionMigrationAsync(
            Guid reportId,
            Guid versionId,
            CancellationToken cancellationToken = default)
        {
            // For now, delegate to the report rollback since we're migrating current versions
            return await RollbackReportMigrationAsync(reportId, cancellationToken);
        }

        /// <summary>
        /// Performs a dry run migration without making actual changes
        /// </summary>
        public async Task<DryRunResult> PerformDryRunAsync(
            Guid? reportId = null,
            MigrationOptions? options = null,
            CancellationToken cancellationToken = default)
        {
            var result = new DryRunResult();

            try
            {
                _logger.LogInformation("Starting dry run migration. ReportId: {ReportId}", reportId);

                if (reportId.HasValue)
                {
                    var report = await _context.Reports
                        .FirstOrDefaultAsync(r => r.Id == reportId.Value && !r.IsDeleted, cancellationToken);

                    if (report != null)
                    {
                        result.ReportsToMigrate = 1;
                        result.VersionsToMigrate = 1;
                        result.EstimatedDataSize = 1024; // Placeholder
                        result.EstimatedDuration = TimeSpan.FromMinutes(1);
                    }
                }
                else
                {
                    var totalReports = await _context.Reports
                        .Where(r => !r.IsDeleted)
                        .CountAsync(cancellationToken);

                    var alreadyMigrated = await _context.Reports
                        .Where(r => !r.IsDeleted && _context.ReportSections.Any(s => s.ReportId == r.Id))
                        .CountAsync(cancellationToken);

                    result.ReportsToMigrate = totalReports - alreadyMigrated;
                    result.VersionsToMigrate = result.ReportsToMigrate;
                    result.EstimatedDataSize = result.ReportsToMigrate * 1024; // Placeholder
                    result.EstimatedDuration = TimeSpan.FromMinutes(result.ReportsToMigrate);
                }

                result.Success = true;
                _logger.LogInformation("Dry run completed. Reports to migrate: {Count}", result.ReportsToMigrate);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.PotentialIssues.Add($"Dry run failed: {ex.Message}");
                _logger.LogError(ex, "Dry run migration failed");
            }

            return result;
        }

        /// <summary>
        /// Gets migration progress for a specific operation
        /// </summary>
        public async Task<MigrationProgress> GetMigrationProgressAsync(
            Guid operationId,
            CancellationToken cancellationToken = default)
        {
            // For now, return a basic progress since we don't track operations yet
            return new MigrationProgress
            {
                OperationId = operationId,
                CurrentPhase = MigrationPhase.Completed,
                TotalReports = 0,
                CompletedReports = 0,
                EstimatedTimeRemaining = TimeSpan.Zero,
                LastUpdateTime = DateTime.UtcNow
            };
        }

        /// <summary>
        /// Cancels an ongoing migration operation
        /// </summary>
        public async Task<CancellationResult> CancelMigrationAsync(
            Guid operationId,
            CancellationToken cancellationToken = default)
        {
            // For now, return a basic cancellation result since we don't track operations yet
            return new CancellationResult
            {
                Success = false,
                Message = "Operation cancellation not implemented yet"
            };
        }

        /// <summary>
        /// Gets a list of reports that need migration
        /// </summary>
        public async Task<IEnumerable<ReportMigrationInfo>> GetReportsRequiringMigrationAsync(
            Guid? tenantId = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var query = _context.Reports
                    .Where(r => !r.IsDeleted && !_context.ReportSections.Any(s => s.ReportId == r.Id));

                if (tenantId.HasValue)
                {
                    query = query.Where(r => r.TenantId == tenantId.Value);
                }

                var reports = await query.ToListAsync(cancellationToken);

                return reports.Select(r => new ReportMigrationInfo
                {
                    ReportId = r.Id,
                    ReportName = r.Name,
                    TenantId = r.TenantId ?? Guid.Empty,
                    LastModified = r.LastModificationTime ?? r.CreationTime,
                    IsMigrated = false
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting reports requiring migration");
                throw;
            }
        }

        /// <summary>
        /// Gets migration history for a specific report
        /// </summary>
        public async Task<IEnumerable<MigrationHistoryEntry>> GetMigrationHistoryAsync(
            Guid reportId,
            CancellationToken cancellationToken = default)
        {
            // For now, return empty history since we don't track migration history yet
            return new List<MigrationHistoryEntry>();
        }

        /// <summary>
        /// Cleans up failed migration artifacts
        /// </summary>
        public async Task<CleanupResult> CleanupFailedMigrationAsync(
            Guid? reportId = null,
            CancellationToken cancellationToken = default)
        {
            var result = new CleanupResult();

            try
            {
                _logger.LogInformation("Starting cleanup of failed migration artifacts. ReportId: {ReportId}", reportId);

                // For now, just return success since we don't have complex cleanup logic yet
                result.Success = true;
                result.ItemsCleanedUp = 0;
                result.Duration = TimeSpan.FromSeconds(1);

                _logger.LogInformation("Cleanup completed successfully");
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Duration = TimeSpan.FromSeconds(1);
                result.Errors.Add($"Cleanup failed: {ex.Message}");

                _logger.LogError(ex, "Cleanup failed");
                throw;
            }

            return result;
        }

        #endregion
    }
}
