# SQL Structure Optimization Plan for Multi-Storage Architecture

## Overview

This document outlines the comprehensive plan to optimize the SQL table structure for multi-storage compatibility based on the analysis of current entities against the target multi-storage design.

## Current State Analysis Summary

### Key Findings
1. **Hybrid Transition State**: System is partially migrated with redundant storage patterns
2. **Data Duplication**: ReportVersion contains both SQL JSON data and storage references
3. **Conflicting Purposes**: Multiple columns serving similar functions
4. **FK Relationship Misalignment**: Traditional relationships don't align with multi-storage patterns

### Architecture Clarifications
- **ReportVersion**: Remains in SQL as authoritative version control system
- **Report Generation**: Dynamic generation based on client/report selection + data filters
- **Template System**: Immutable templates with inheritance (no direct modification)
- **Draft-Based Editing**: Reports exist in draft state until Save operation
- **Flexible Creation**: Template-based OR scratch-built reports

## Phase 1: EF Core Migration Implementation

### 1.1 Entity Structure Changes

#### ReportVersion Entity (Optimized)
```csharp
public class ReportVersion : AuditedEntity<Guid>
{
    // Core version metadata (keep in SQL)
    public Guid ReportId { get; set; }
    public int VersionNumber { get; set; }
    public string Description { get; set; } = string.Empty;
    public bool IsCurrent { get; set; }
    public DateTime CreatedAt { get; set; }
    
    // Multi-storage references (versioned)
    public string? DataDocumentId { get; set; }        // Cosmos DB document for this version
    public string? ComponentsBlobId { get; set; }      // Blob storage for components
    public string? StylesBlobId { get; set; }          // NEW: Versioned styles in blob storage
    
    // Storage size tracking (for monitoring and cost optimization)
    public long DataSize { get; set; }
    public long ComponentsSize { get; set; }
    public long StylesSize { get; set; }
    
    // Storage strategy for this version
    public string StorageStrategy { get; set; } = "SQL"; // SQL, Hybrid, MultiStorage
    
    // Navigation properties
    public virtual Report Report { get; set; } = null!;
    public virtual ICollection<ComponentDefinition> ComponentDefinitions { get; set; } = new List<ComponentDefinition>();
    
    // REMOVE these columns in migration:
    // - JsonData (moved to Cosmos DB)
    // - ComponentDataJson (moved to Blob Storage)
    // - ComponentDataSize (renamed to ComponentsSize)
    // - JsonDataSize (renamed to DataSize)
    // - DataBlobPath (replaced by ComponentsBlobId)
    // - StyleDocumentId (replaced by StylesBlobId)
    // - IsDataInBlob (replaced by StorageStrategy)
}
```

#### Report Entity (Simplified)
```csharp
public class Report : FullAuditedMultiTenantEntity<Guid>
{
    // Core metadata (keep in SQL for fast queries)
    public string ReportNumber { get; set; } = string.Empty;
    public Guid ClientId { get; set; }
    public string ClientName { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public int SlideCount { get; set; }
    public string Status { get; set; } = string.Empty;
    public string Author { get; set; } = string.Empty;
    public string ReportType { get; set; } = "Standard";
    
    // Template and version management
    public Guid? TemplateId { get; set; }
    public Guid? CurrentVersionId { get; set; }
    
    // Draft state management (NEW)
    public bool IsDraft { get; set; } = true;
    public DateTime? LastSavedAt { get; set; }
    public string? DraftDataDocumentId { get; set; }  // Cosmos DB for draft data
    
    // Navigation properties
    public virtual Client? Client { get; set; }
    public virtual Template? Template { get; set; }
    public virtual ICollection<ReportVersion> Versions { get; set; } = new List<ReportVersion>();
    public virtual ICollection<ReportSection> Sections { get; set; } = new List<ReportSection>();
    
    // REMOVE these columns in migration:
    // - DataDocumentId (handled at version level)
    // - ComponentsBlobId (handled at version level)
}
```

#### ReportSection Entity (Migration Support)
```csharp
public class ReportSection : AuditedEntity<Guid>
{
    public Guid ReportId { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public int Order { get; set; }
    
    // Migration tracking (NEW)
    public bool IsMigratedToCosmos { get; set; } = false;
    public string? CosmosDocumentId { get; set; }
    public DateTime? MigrationDate { get; set; }
    
    // Template inheritance tracking (NEW)
    public Guid? TemplateSourceSectionId { get; set; }  // If inherited from template
    public bool IsModifiedFromTemplate { get; set; } = false;
    
    public virtual Report Report { get; set; } = null!;
    public virtual ICollection<ReportSectionField> Fields { get; set; } = new List<ReportSectionField>();
}
```

#### ReportSectionField Entity (Migration Support)
```csharp
public class ReportSectionField : AuditedEntity<Guid>
{
    public Guid SectionId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public int Order { get; set; }
    
    // Migration tracking (NEW)
    public bool IsMigratedToCosmos { get; set; } = false;
    public DateTime? MigrationDate { get; set; }
    
    // Template inheritance tracking (NEW)
    public Guid? TemplateSourceFieldId { get; set; }  // If inherited from template
    public bool IsModifiedFromTemplate { get; set; } = false;
    
    public virtual ReportSection Section { get; set; } = null!;
}
```

#### New Entity: ReportStorageMetadata
```csharp
public class ReportStorageMetadata : AuditedEntity<Guid>
{
    public Guid ReportId { get; set; }
    
    // Storage strategy tracking
    public string StorageStrategy { get; set; } = "SQL"; // SQL, Hybrid, MultiStorage
    public string MigrationStatus { get; set; } = "NotMigrated"; // NotMigrated, InProgress, Completed, Failed
    public DateTime? MigrationStartDate { get; set; }
    public DateTime? MigrationCompletedDate { get; set; }
    public string? MigrationErrorMessage { get; set; }
    
    // Storage size tracking for cost optimization
    public long SqlStorageSize { get; set; }
    public long CosmosStorageSize { get; set; }
    public long BlobStorageSize { get; set; }
    public long TotalStorageSize { get; set; }
    
    // Performance tracking
    public DateTime? LastAccessTime { get; set; }
    public int AccessCount { get; set; } = 0;
    
    public virtual Report Report { get; set; } = null!;
}
```

### 1.2 Migration Script

```csharp
public partial class OptimizeMultiStorageStructure : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        // 1. Add new columns to ReportVersions
        migrationBuilder.AddColumn<string>(
            name: "StylesBlobId",
            table: "ReportVersions",
            type: "nvarchar(255)",
            maxLength: 255,
            nullable: true);
            
        migrationBuilder.AddColumn<long>(
            name: "StylesSize",
            table: "ReportVersions",
            type: "bigint",
            nullable: false,
            defaultValue: 0L);
            
        migrationBuilder.AddColumn<string>(
            name: "StorageStrategy",
            table: "ReportVersions",
            type: "nvarchar(50)",
            maxLength: 50,
            nullable: false,
            defaultValue: "SQL");
        
        // 2. Add new columns to Reports
        migrationBuilder.AddColumn<bool>(
            name: "IsDraft",
            table: "Reports",
            type: "bit",
            nullable: false,
            defaultValue: true);
            
        migrationBuilder.AddColumn<DateTime>(
            name: "LastSavedAt",
            table: "Reports",
            type: "datetime2",
            nullable: true);
            
        migrationBuilder.AddColumn<string>(
            name: "DraftDataDocumentId",
            table: "Reports",
            type: "nvarchar(255)",
            maxLength: 255,
            nullable: true);
        
        // 3. Add migration tracking to ReportSections
        migrationBuilder.AddColumn<bool>(
            name: "IsMigratedToCosmos",
            table: "ReportSections",
            type: "bit",
            nullable: false,
            defaultValue: false);
            
        migrationBuilder.AddColumn<string>(
            name: "CosmosDocumentId",
            table: "ReportSections",
            type: "nvarchar(255)",
            maxLength: 255,
            nullable: true);
            
        migrationBuilder.AddColumn<DateTime>(
            name: "MigrationDate",
            table: "ReportSections",
            type: "datetime2",
            nullable: true);
            
        migrationBuilder.AddColumn<Guid>(
            name: "TemplateSourceSectionId",
            table: "ReportSections",
            type: "uniqueidentifier",
            nullable: true);
            
        migrationBuilder.AddColumn<bool>(
            name: "IsModifiedFromTemplate",
            table: "ReportSections",
            type: "bit",
            nullable: false,
            defaultValue: false);
        
        // 4. Add migration tracking to ReportSectionFields
        migrationBuilder.AddColumn<bool>(
            name: "IsMigratedToCosmos",
            table: "ReportSectionFields",
            type: "bit",
            nullable: false,
            defaultValue: false);
            
        migrationBuilder.AddColumn<DateTime>(
            name: "MigrationDate",
            table: "ReportSectionFields",
            type: "datetime2",
            nullable: true);
            
        migrationBuilder.AddColumn<Guid>(
            name: "TemplateSourceFieldId",
            table: "ReportSectionFields",
            type: "uniqueidentifier",
            nullable: true);
            
        migrationBuilder.AddColumn<bool>(
            name: "IsModifiedFromTemplate",
            table: "ReportSectionFields",
            type: "bit",
            nullable: false,
            defaultValue: false);
        
        // 5. Create ReportStorageMetadata table
        migrationBuilder.CreateTable(
            name: "ReportStorageMetadata",
            columns: table => new
            {
                Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                ReportId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                StorageStrategy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, defaultValue: "SQL"),
                MigrationStatus = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, defaultValue: "NotMigrated"),
                MigrationStartDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                MigrationCompletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                MigrationErrorMessage = table.Column<string>(type: "nvarchar(max)", nullable: true),
                SqlStorageSize = table.Column<long>(type: "bigint", nullable: false, defaultValue: 0L),
                CosmosStorageSize = table.Column<long>(type: "bigint", nullable: false, defaultValue: 0L),
                BlobStorageSize = table.Column<long>(type: "bigint", nullable: false, defaultValue: 0L),
                TotalStorageSize = table.Column<long>(type: "bigint", nullable: false, defaultValue: 0L),
                LastAccessTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                AccessCount = table.Column<int>(type: "int", nullable: false, defaultValue: 0),
                CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_ReportStorageMetadata", x => x.Id);
                table.ForeignKey(
                    name: "FK_ReportStorageMetadata_Reports_ReportId",
                    column: x => x.ReportId,
                    principalTable: "Reports",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
            });
        
        // 6. Create indexes for performance
        migrationBuilder.CreateIndex(
            name: "IX_ReportVersions_StorageStrategy",
            table: "ReportVersions",
            column: "StorageStrategy");
            
        migrationBuilder.CreateIndex(
            name: "IX_ReportVersions_StylesBlobId",
            table: "ReportVersions",
            column: "StylesBlobId")
            .Annotation("SqlServer:Include", new[] { "ReportId", "VersionNumber" });
            
        migrationBuilder.CreateIndex(
            name: "IX_Reports_IsDraft",
            table: "Reports",
            column: "IsDraft");
            
        migrationBuilder.CreateIndex(
            name: "IX_Reports_DraftDataDocumentId",
            table: "Reports",
            column: "DraftDataDocumentId")
            .Annotation("SqlServer:Include", new[] { "TenantId" });
            
        migrationBuilder.CreateIndex(
            name: "IX_ReportSections_IsMigratedToCosmos",
            table: "ReportSections",
            column: "IsMigratedToCosmos");
            
        migrationBuilder.CreateIndex(
            name: "IX_ReportSections_CosmosDocumentId",
            table: "ReportSections",
            column: "CosmosDocumentId");
            
        migrationBuilder.CreateIndex(
            name: "IX_ReportStorageMetadata_ReportId",
            table: "ReportStorageMetadata",
            column: "ReportId",
            unique: true);
            
        migrationBuilder.CreateIndex(
            name: "IX_ReportStorageMetadata_StorageStrategy",
            table: "ReportStorageMetadata",
            column: "StorageStrategy");
            
        migrationBuilder.CreateIndex(
            name: "IX_ReportStorageMetadata_MigrationStatus",
            table: "ReportStorageMetadata",
            column: "MigrationStatus");
        
        // 7. Rename existing columns for consistency
        migrationBuilder.RenameColumn(
            name: "ComponentDataSize",
            table: "ReportVersions",
            newName: "ComponentsSize");
            
        migrationBuilder.RenameColumn(
            name: "JsonDataSize",
            table: "ReportVersions",
            newName: "DataSize");
    }
    
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        // Drop new table
        migrationBuilder.DropTable(name: "ReportStorageMetadata");
        
        // Remove new columns from ReportVersions
        migrationBuilder.DropColumn(name: "StylesBlobId", table: "ReportVersions");
        migrationBuilder.DropColumn(name: "StylesSize", table: "ReportVersions");
        migrationBuilder.DropColumn(name: "StorageStrategy", table: "ReportVersions");
        
        // Remove new columns from Reports
        migrationBuilder.DropColumn(name: "IsDraft", table: "Reports");
        migrationBuilder.DropColumn(name: "LastSavedAt", table: "Reports");
        migrationBuilder.DropColumn(name: "DraftDataDocumentId", table: "Reports");
        
        // Remove new columns from ReportSections
        migrationBuilder.DropColumn(name: "IsMigratedToCosmos", table: "ReportSections");
        migrationBuilder.DropColumn(name: "CosmosDocumentId", table: "ReportSections");
        migrationBuilder.DropColumn(name: "MigrationDate", table: "ReportSections");
        migrationBuilder.DropColumn(name: "TemplateSourceSectionId", table: "ReportSections");
        migrationBuilder.DropColumn(name: "IsModifiedFromTemplate", table: "ReportSections");
        
        // Remove new columns from ReportSectionFields
        migrationBuilder.DropColumn(name: "IsMigratedToCosmos", table: "ReportSectionFields");
        migrationBuilder.DropColumn(name: "MigrationDate", table: "ReportSectionFields");
        migrationBuilder.DropColumn(name: "TemplateSourceFieldId", table: "ReportSectionFields");
        migrationBuilder.DropColumn(name: "IsModifiedFromTemplate", table: "ReportSectionFields");
        
        // Restore original column names
        migrationBuilder.RenameColumn(
            name: "ComponentsSize",
            table: "ReportVersions",
            newName: "ComponentDataSize");
            
        migrationBuilder.RenameColumn(
            name: "DataSize",
            table: "ReportVersions",
            newName: "JsonDataSize");
    }
}
```

### 1.3 DbContext Configuration Updates

```csharp
// In ApplicationDbContext.cs
protected override void OnModelCreating(ModelBuilder modelBuilder)
{
    base.OnModelCreating(modelBuilder);
    
    // ReportVersion configuration
    modelBuilder.Entity<ReportVersion>(entity =>
    {
        entity.Property(e => e.StorageStrategy)
            .HasMaxLength(50)
            .HasDefaultValue("SQL");
            
        entity.Property(e => e.StylesBlobId)
            .HasMaxLength(255);
            
        entity.HasIndex(e => e.StorageStrategy)
            .HasDatabaseName("IX_ReportVersions_StorageStrategy");
            
        entity.HasIndex(e => e.StylesBlobId)
            .HasDatabaseName("IX_ReportVersions_StylesBlobId")
            .IncludeProperties(e => new { e.ReportId, e.VersionNumber });
    });
    
    // Report configuration
    modelBuilder.Entity<Report>(entity =>
    {
        entity.Property(e => e.IsDraft)
            .HasDefaultValue(true);
            
        entity.Property(e => e.DraftDataDocumentId)
            .HasMaxLength(255);
            
        entity.HasIndex(e => e.IsDraft)
            .HasDatabaseName("IX_Reports_IsDraft");
            
        entity.HasIndex(e => e.DraftDataDocumentId)
            .HasDatabaseName("IX_Reports_DraftDataDocumentId")
            .IncludeProperties(e => e.TenantId);
    });
    
    // ReportStorageMetadata configuration
    modelBuilder.Entity<ReportStorageMetadata>(entity =>
    {
        entity.Property(e => e.StorageStrategy)
            .HasMaxLength(50)
            .HasDefaultValue("SQL");
            
        entity.Property(e => e.MigrationStatus)
            .HasMaxLength(50)
            .HasDefaultValue("NotMigrated");
            
        entity.HasOne(e => e.Report)
            .WithOne()
            .HasForeignKey<ReportStorageMetadata>(e => e.ReportId)
            .OnDelete(DeleteBehavior.Cascade);
            
        entity.HasIndex(e => e.ReportId)
            .IsUnique()
            .HasDatabaseName("IX_ReportStorageMetadata_ReportId");
            
        entity.HasIndex(e => e.StorageStrategy)
            .HasDatabaseName("IX_ReportStorageMetadata_StorageStrategy");
            
        entity.HasIndex(e => e.MigrationStatus)
            .HasDatabaseName("IX_ReportStorageMetadata_MigrationStatus");
    });
}
```

## Phase 2: Cleanup Migration (Future)

After successful migration to multi-storage, a second migration will remove redundant columns:

```csharp
public partial class RemoveRedundantStorageColumns : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        // Remove redundant columns from ReportVersions
        migrationBuilder.DropColumn(name: "JsonData", table: "ReportVersions");
        migrationBuilder.DropColumn(name: "ComponentDataJson", table: "ReportVersions");
        migrationBuilder.DropColumn(name: "DataBlobPath", table: "ReportVersions");
        migrationBuilder.DropColumn(name: "StyleDocumentId", table: "ReportVersions");
        migrationBuilder.DropColumn(name: "IsDataInBlob", table: "ReportVersions");
        
        // Remove redundant columns from Reports
        migrationBuilder.DropColumn(name: "DataDocumentId", table: "Reports");
        migrationBuilder.DropColumn(name: "ComponentsBlobId", table: "Reports");
    }
}
```

## Implementation Notes

### Migration Safety
1. **Backward Compatibility**: New columns have appropriate defaults
2. **Gradual Migration**: Old columns remain until migration is complete
3. **Rollback Support**: Down migration restores original structure
4. **Index Optimization**: New indexes support multi-storage query patterns

### Performance Considerations
1. **Selective Indexes**: Include columns for common query patterns
2. **Storage Size Tracking**: Monitor storage costs across systems
3. **Access Tracking**: Identify frequently accessed reports for optimization

### Development Workflow
1. **Feature Flags**: Enable gradual rollout of multi-storage features
2. **Validation**: Ensure data consistency during migration
3. **Monitoring**: Track migration progress and performance impact

This migration provides the foundation for the multi-storage architecture while maintaining backward compatibility and supporting the draft-based editing workflow.
