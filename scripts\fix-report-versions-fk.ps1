# Fix Report-Versions Foreign Key Mismatch
# This script corrects the foreign key references in report-versions.json

param(
    [switch]$WhatIf = $false
)

$ErrorActionPreference = "Stop"

# File paths
$reportsPath = "FY.WB.CSHero2.Infrastructure/Persistence/SeedData/reports.json"
$reportVersionsPath = "FY.WB.CSHero2.Infrastructure/Persistence/SeedData/report-versions.json"
$backupPath = "FY.WB.CSHero2.Infrastructure/Persistence/SeedData/report-versions.json.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"

Write-Host "=== Foreign Key Mismatch Fix ===" -ForegroundColor Cyan
Write-Host "Reports file: $reportsPath" -ForegroundColor Gray
Write-Host "ReportVersions file: $reportVersionsPath" -ForegroundColor Gray

# Verify files exist
if (-not (Test-Path $reportsPath)) {
    throw "Reports file not found: $reportsPath"
}
if (-not (Test-Path $reportVersionsPath)) {
    throw "ReportVersions file not found: $reportVersionsPath"
}

# Load data
Write-Host "`nLoading data..." -ForegroundColor Yellow
$reports = Get-Content $reportsPath -Raw | ConvertFrom-Json
$reportVersions = Get-Content $reportVersionsPath -Raw | ConvertFrom-Json

Write-Host "Reports loaded: $($reports.Count)" -ForegroundColor Green
Write-Host "ReportVersions loaded: $($reportVersions.Count)" -ForegroundColor Green

# Create lookup of Report IDs by index
$reportIdsByIndex = @{}
for ($i = 0; $i -lt $reports.Count; $i++) {
    $reportIdsByIndex[$i] = $reports[$i].id
}

Write-Host "`nReport IDs by index:" -ForegroundColor Yellow
for ($i = 0; $i -lt [Math]::Min(15, $reports.Count); $i++) {
    Write-Host "  [$i] = $($reportIdsByIndex[$i])" -ForegroundColor Gray
}

# Identify mismatches
Write-Host "`nAnalyzing foreign key relationships..." -ForegroundColor Yellow
$mismatches = @()
$reportIdSet = @{}
foreach ($report in $reports) {
    $reportIdSet[$report.id] = $true
}

for ($i = 0; $i -lt $reportVersions.Count; $i++) {
    $version = $reportVersions[$i]
    $reportId = $version.reportId
    
    if (-not $reportIdSet.ContainsKey($reportId)) {
        $mismatches += @{
            Index = $i
            VersionId = $version.id
            CurrentReportId = $reportId
            ShouldBeReportId = $reportIdsByIndex[$i]
        }
        Write-Host "  MISMATCH [$i]: Version $($version.id) references non-existent Report $reportId" -ForegroundColor Red
        Write-Host "    Should reference: $($reportIdsByIndex[$i])" -ForegroundColor Green
    }
}

if ($mismatches.Count -eq 0) {
    Write-Host "No foreign key mismatches found!" -ForegroundColor Green
    exit 0
}

Write-Host "`nFound $($mismatches.Count) foreign key mismatches" -ForegroundColor Red

if ($WhatIf) {
    Write-Host "`n=== WHAT-IF MODE: No changes will be made ===" -ForegroundColor Magenta
    foreach ($mismatch in $mismatches) {
        Write-Host "Would fix ReportVersion [$($mismatch.Index)]:" -ForegroundColor Yellow
        Write-Host "  From: $($mismatch.CurrentReportId)" -ForegroundColor Red
        Write-Host "  To:   $($mismatch.ShouldBeReportId)" -ForegroundColor Green
    }
    exit 0
}

# Create backup
Write-Host "`nCreating backup: $backupPath" -ForegroundColor Yellow
Copy-Item $reportVersionsPath $backupPath

# Fix the mismatches
Write-Host "`nFixing foreign key mismatches..." -ForegroundColor Yellow
foreach ($mismatch in $mismatches) {
    $index = $mismatch.Index
    $oldReportId = $reportVersions[$index].reportId
    $newReportId = $mismatch.ShouldBeReportId
    
    $reportVersions[$index].reportId = $newReportId
    
    Write-Host "  Fixed ReportVersion [$index]: $oldReportId -> $newReportId" -ForegroundColor Green
}

# Save updated data
Write-Host "`nSaving updated report-versions.json..." -ForegroundColor Yellow
$reportVersions | ConvertTo-Json -Depth 10 | Set-Content $reportVersionsPath -Encoding UTF8

# Verify the fix
Write-Host "`nVerifying fix..." -ForegroundColor Yellow
$updatedVersions = Get-Content $reportVersionsPath -Raw | ConvertFrom-Json
$remainingMismatches = 0

for ($i = 0; $i -lt $updatedVersions.Count; $i++) {
    $version = $updatedVersions[$i]
    $reportId = $version.reportId
    
    if (-not $reportIdSet.ContainsKey($reportId)) {
        $remainingMismatches++
        Write-Host "  STILL BROKEN [$i]: $reportId" -ForegroundColor Red
    }
}

if ($remainingMismatches -eq 0) {
    Write-Host "✅ All foreign key mismatches fixed successfully!" -ForegroundColor Green
    Write-Host "`nSummary:" -ForegroundColor Cyan
    Write-Host "  - Fixed $($mismatches.Count) foreign key mismatches" -ForegroundColor Green
    Write-Host "  - Backup created: $backupPath" -ForegroundColor Gray
    Write-Host "  - All $($updatedVersions.Count) ReportVersions now reference valid Reports" -ForegroundColor Green
} else {
    Write-Host "❌ $remainingMismatches foreign key mismatches remain!" -ForegroundColor Red
    exit 1
}
