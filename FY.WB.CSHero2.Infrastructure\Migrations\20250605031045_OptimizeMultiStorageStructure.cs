﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FY.WB.CSHero2.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class OptimizeMultiStorageStructure : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "StorageStrategy",
                table: "ReportVersions",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "SQL");

            migrationBuilder.AddColumn<string>(
                name: "StylesBlobId",
                table: "ReportVersions",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "StylesSize",
                table: "ReportVersions",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<string>(
                name: "CosmosDocumentId",
                table: "ReportSections",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsMigratedToCosmos",
                table: "ReportSections",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsModifiedFromTemplate",
                table: "ReportSections",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateTime>(
                name: "MigrationDate",
                table: "ReportSections",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "TemplateSourceSectionId",
                table: "ReportSections",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsMigratedToCosmos",
                table: "ReportSectionFields",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsModifiedFromTemplate",
                table: "ReportSectionFields",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateTime>(
                name: "MigrationDate",
                table: "ReportSectionFields",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "TemplateSourceFieldId",
                table: "ReportSectionFields",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DraftDataDocumentId",
                table: "Reports",
                type: "nvarchar(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsDraft",
                table: "Reports",
                type: "bit",
                nullable: false,
                defaultValue: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastSavedAt",
                table: "Reports",
                type: "datetime2",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "ReportStorageMetadata",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ReportId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    StorageStrategy = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, defaultValue: "SQL"),
                    MigrationStatus = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, defaultValue: "NotMigrated"),
                    MigrationStartDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    MigrationCompletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    MigrationErrorMessage = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    SqlStorageSize = table.Column<long>(type: "bigint", nullable: false, defaultValue: 0L),
                    CosmosStorageSize = table.Column<long>(type: "bigint", nullable: false, defaultValue: 0L),
                    BlobStorageSize = table.Column<long>(type: "bigint", nullable: false, defaultValue: 0L),
                    TotalStorageSize = table.Column<long>(type: "bigint", nullable: false, defaultValue: 0L),
                    AccessCount = table.Column<long>(type: "bigint", nullable: false, defaultValue: 0L),
                    LastAccessDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    PerformanceMetrics = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OptimizationMetadata = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ReportStorageMetadata", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ReportStorageMetadata_Reports_ReportId",
                        column: x => x.ReportId,
                        principalTable: "Reports",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ReportVersions_StorageStrategy",
                table: "ReportVersions",
                column: "StorageStrategy");

            migrationBuilder.CreateIndex(
                name: "IX_ReportSections_IsMigratedToCosmos",
                table: "ReportSections",
                column: "IsMigratedToCosmos");

            migrationBuilder.CreateIndex(
                name: "IX_ReportSections_TemplateSourceSectionId",
                table: "ReportSections",
                column: "TemplateSourceSectionId");

            migrationBuilder.CreateIndex(
                name: "IX_ReportSectionFields_IsMigratedToCosmos",
                table: "ReportSectionFields",
                column: "IsMigratedToCosmos");

            migrationBuilder.CreateIndex(
                name: "IX_ReportSectionFields_TemplateSourceFieldId",
                table: "ReportSectionFields",
                column: "TemplateSourceFieldId");

            migrationBuilder.CreateIndex(
                name: "IX_Reports_IsDraft",
                table: "Reports",
                column: "IsDraft");

            migrationBuilder.CreateIndex(
                name: "IX_Reports_LastSavedAt",
                table: "Reports",
                column: "LastSavedAt");

            migrationBuilder.CreateIndex(
                name: "IX_ReportStorageMetadata_LastAccessDate",
                table: "ReportStorageMetadata",
                column: "LastAccessDate");

            migrationBuilder.CreateIndex(
                name: "IX_ReportStorageMetadata_MigrationStatus",
                table: "ReportStorageMetadata",
                column: "MigrationStatus");

            migrationBuilder.CreateIndex(
                name: "IX_ReportStorageMetadata_ReportId",
                table: "ReportStorageMetadata",
                column: "ReportId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ReportStorageMetadata_StorageStrategy",
                table: "ReportStorageMetadata",
                column: "StorageStrategy");

            migrationBuilder.CreateIndex(
                name: "IX_ReportStorageMetadata_TotalStorageSize",
                table: "ReportStorageMetadata",
                column: "TotalStorageSize");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ReportStorageMetadata");

            migrationBuilder.DropIndex(
                name: "IX_ReportVersions_StorageStrategy",
                table: "ReportVersions");

            migrationBuilder.DropIndex(
                name: "IX_ReportSections_IsMigratedToCosmos",
                table: "ReportSections");

            migrationBuilder.DropIndex(
                name: "IX_ReportSections_TemplateSourceSectionId",
                table: "ReportSections");

            migrationBuilder.DropIndex(
                name: "IX_ReportSectionFields_IsMigratedToCosmos",
                table: "ReportSectionFields");

            migrationBuilder.DropIndex(
                name: "IX_ReportSectionFields_TemplateSourceFieldId",
                table: "ReportSectionFields");

            migrationBuilder.DropIndex(
                name: "IX_Reports_IsDraft",
                table: "Reports");

            migrationBuilder.DropIndex(
                name: "IX_Reports_LastSavedAt",
                table: "Reports");

            migrationBuilder.DropColumn(
                name: "StorageStrategy",
                table: "ReportVersions");

            migrationBuilder.DropColumn(
                name: "StylesBlobId",
                table: "ReportVersions");

            migrationBuilder.DropColumn(
                name: "StylesSize",
                table: "ReportVersions");

            migrationBuilder.DropColumn(
                name: "CosmosDocumentId",
                table: "ReportSections");

            migrationBuilder.DropColumn(
                name: "IsMigratedToCosmos",
                table: "ReportSections");

            migrationBuilder.DropColumn(
                name: "IsModifiedFromTemplate",
                table: "ReportSections");

            migrationBuilder.DropColumn(
                name: "MigrationDate",
                table: "ReportSections");

            migrationBuilder.DropColumn(
                name: "TemplateSourceSectionId",
                table: "ReportSections");

            migrationBuilder.DropColumn(
                name: "IsMigratedToCosmos",
                table: "ReportSectionFields");

            migrationBuilder.DropColumn(
                name: "IsModifiedFromTemplate",
                table: "ReportSectionFields");

            migrationBuilder.DropColumn(
                name: "MigrationDate",
                table: "ReportSectionFields");

            migrationBuilder.DropColumn(
                name: "TemplateSourceFieldId",
                table: "ReportSectionFields");

            migrationBuilder.DropColumn(
                name: "DraftDataDocumentId",
                table: "Reports");

            migrationBuilder.DropColumn(
                name: "IsDraft",
                table: "Reports");

            migrationBuilder.DropColumn(
                name: "LastSavedAt",
                table: "Reports");
        }
    }
}
