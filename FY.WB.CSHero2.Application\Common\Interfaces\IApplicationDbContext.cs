using FY.WB.CSHero2.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.Common.Interfaces
{
    public interface IApplicationDbContext
    {
        // Entity DbSets
        DbSet<Client> Clients { get; }
        DbSet<Report> Reports { get; }
        DbSet<Invoice> Invoices { get; }
        DbSet<Template> Templates { get; }
        DbSet<Form> Forms { get; }
        DbSet<Upload> Uploads { get; }
        DbSet<TenantProfile> TenantProfiles { get; }

        // Report Structure DbSets
        DbSet<ReportSection> ReportSections { get; }
        DbSet<ReportSectionField> ReportSectionFields { get; }
        // V2 Report Rendering Engine DbSets
        DbSet<ReportVersion> ReportVersions { get; }
        DbSet<ComponentDefinition> ComponentDefinitions { get; }

        // V3 Multi-storage DbSets
        DbSet<ReportStyle> ReportStyles { get; }
        DbSet<ReportStorageMetadata> ReportStorageMetadata { get; }

        Task<int> SaveChangesAsync(CancellationToken cancellationToken);
    }
}
