# Diagnose Foreign Key Issues in Seed Data
# This script identifies mismatches between related entities in JSON seed files

Write-Host "=== Seed Data Foreign Key Diagnostic Tool ===" -ForegroundColor Cyan
Write-Host "Analyzing relationships between seed data files..." -ForegroundColor Yellow

$seedDataPath = "FY.WB.CSHero2.Infrastructure\Persistence\SeedData"

# Read all seed data files
try {
    $clients = Get-Content "$seedDataPath\clients.json" | ConvertFrom-Json
    $reports = Get-Content "$seedDataPath\reports.json" | ConvertFrom-Json
    $reportVersions = Get-Content "$seedDataPath\report-versions.json" | ConvertFrom-Json
    $reportSections = Get-Content "$seedDataPath\report-sections.json" | ConvertFrom-Json
    $reportSectionFields = Get-Content "$seedDataPath\report-section-fields.json" | ConvertFrom-Json
    $tenantProfiles = Get-Content "$seedDataPath\tenant-profiles.json" | ConvertFrom-Json
} catch {
    Write-Host "Error reading seed data files: $_" -ForegroundColor Red
    exit 1
}

Write-Host "`n1. ANALYZING CLIENT-REPORT RELATIONSHIPS" -ForegroundColor Green
Write-Host "=" * 50

# Create lookup tables
$clientLookup = @{}
$clients | ForEach-Object { $clientLookup[$_.Id] = $_ }

$clientByCompanyAndTenant = @{}
$clients | ForEach-Object { 
    $key = "$($_.CompanyName)|$($_.TenantId)"
    $clientByCompanyAndTenant[$key] = $_
}

$reportLookup = @{}
$reports | ForEach-Object { $reportLookup[$_.id] = $_ }

# Check Reports -> Clients relationships
$invalidClientRefs = @()
$validReports = @()
$skippedReports = @()

foreach ($report in $reports) {
    # Check if clientId exists in clients
    $clientExists = $clientLookup.ContainsKey($report.clientId)
    
    # Check if clientName + tenantId combination exists
    $lookupKey = "$($report.clientName)|$($report.tenantId)"
    $clientByNameExists = $clientByCompanyAndTenant.ContainsKey($lookupKey)
    
    if (-not $clientExists) {
        $invalidClientRefs += [PSCustomObject]@{
            ReportId = $report.id
            ReportName = $report.name
            ClientId = $report.clientId
            ClientName = $report.clientName
            TenantId = $report.tenantId
            ClientIdExists = $clientExists
            ClientByNameExists = $clientByNameExists
            CorrectClientId = if ($clientByNameExists) { $clientByCompanyAndTenant[$lookupKey].Id } else { "NOT_FOUND" }
        }
        $skippedReports += $report.id
    } else {
        $validReports += $report.id
    }
}

Write-Host "Total Reports: $($reports.Count)" -ForegroundColor White
Write-Host "Valid Client References: $($validReports.Count)" -ForegroundColor Green
Write-Host "Invalid Client References: $($invalidClientRefs.Count)" -ForegroundColor Red

if ($invalidClientRefs.Count -gt 0) {
    Write-Host "`nINVALID CLIENT REFERENCES:" -ForegroundColor Red
    $invalidClientRefs | Format-Table -AutoSize
}

Write-Host "`n2. ANALYZING REPORTVERSIONS -> REPORTS RELATIONSHIPS" -ForegroundColor Green
Write-Host "=" * 50

# Check ReportVersions -> Reports relationships
$orphanedVersions = @()
$validVersions = @()

foreach ($version in $reportVersions) {
    $reportExists = $reportLookup.ContainsKey($version.reportId)
    $reportWillBeSkipped = $skippedReports -contains $version.reportId
    
    if (-not $reportExists -or $reportWillBeSkipped) {
        $orphanedVersions += [PSCustomObject]@{
            VersionId = $version.id
            ReportId = $version.reportId
            Description = $version.description
            ReportExistsInJson = $reportExists
            ReportWillBeSkipped = $reportWillBeSkipped
            Reason = if (-not $reportExists) { "Report ID not found in reports.json" } 
                    elseif ($reportWillBeSkipped) { "Report will be skipped due to invalid client reference" }
                    else { "Unknown" }
        }
    } else {
        $validVersions += $version.reportId
    }
}

Write-Host "Total Report Versions: $($reportVersions.Count)" -ForegroundColor White
Write-Host "Valid Report References: $($validVersions.Count)" -ForegroundColor Green
Write-Host "Orphaned Versions: $($orphanedVersions.Count)" -ForegroundColor Red

if ($orphanedVersions.Count -gt 0) {
    Write-Host "`nORPHANED REPORT VERSIONS:" -ForegroundColor Red
    $orphanedVersions | Format-Table -AutoSize
}

Write-Host "`n3. ANALYZING REPORTSECTIONS -> REPORTS RELATIONSHIPS" -ForegroundColor Green
Write-Host "=" * 50

# Check ReportSections -> Reports relationships
$orphanedSections = @()
$validSections = @()

foreach ($section in $reportSections) {
    $reportExists = $reportLookup.ContainsKey($section.reportId)
    $reportWillBeSkipped = $skippedReports -contains $section.reportId
    
    if (-not $reportExists -or $reportWillBeSkipped) {
        $orphanedSections += [PSCustomObject]@{
            SectionId = $section.id
            ReportId = $section.reportId
            Title = $section.title
            ReportExistsInJson = $reportExists
            ReportWillBeSkipped = $reportWillBeSkipped
        }
    } else {
        $validSections += $section.id
    }
}

Write-Host "Total Report Sections: $($reportSections.Count)" -ForegroundColor White
Write-Host "Valid Report References: $($validSections.Count)" -ForegroundColor Green
Write-Host "Orphaned Sections: $($orphanedSections.Count)" -ForegroundColor Red

if ($orphanedSections.Count -gt 0) {
    Write-Host "`nORPHANED REPORT SECTIONS:" -ForegroundColor Red
    $orphanedSections | Format-Table -AutoSize
}

Write-Host "`n4. SUMMARY AND RECOMMENDATIONS" -ForegroundColor Green
Write-Host "=" * 50

Write-Host "`nISSUES FOUND:" -ForegroundColor Yellow
Write-Host "- $($invalidClientRefs.Count) reports have invalid client references" -ForegroundColor $(if ($invalidClientRefs.Count -gt 0) { "Red" } else { "Green" })
Write-Host "- $($orphanedVersions.Count) report versions will fail FK constraints" -ForegroundColor $(if ($orphanedVersions.Count -gt 0) { "Red" } else { "Green" })
Write-Host "- $($orphanedSections.Count) report sections will fail FK constraints" -ForegroundColor $(if ($orphanedSections.Count -gt 0) { "Red" } else { "Green" })

Write-Host "`nRECOMMENDED ACTIONS:" -ForegroundColor Yellow
if ($invalidClientRefs.Count -gt 0) {
    Write-Host "1. Fix client ID mismatches in reports.json" -ForegroundColor White
}
if ($orphanedVersions.Count -gt 0) {
    Write-Host "2. Remove or fix orphaned report versions" -ForegroundColor White
}
if ($orphanedSections.Count -gt 0) {
    Write-Host "3. Remove or fix orphaned report sections" -ForegroundColor White
}
Write-Host "4. Add FK validation to ReportVersions seeding logic" -ForegroundColor White

# Export detailed results for fixing
$results = @{
    InvalidClientRefs = $invalidClientRefs
    OrphanedVersions = $orphanedVersions
    OrphanedSections = $orphanedSections
    SkippedReports = $skippedReports
}

$results | ConvertTo-Json -Depth 10 | Out-File "seed-data-fk-issues.json" -Encoding UTF8
Write-Host "`nDetailed results exported to: seed-data-fk-issues.json" -ForegroundColor Cyan
