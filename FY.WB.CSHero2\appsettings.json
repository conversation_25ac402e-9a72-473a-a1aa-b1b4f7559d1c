{"Jwt": {"Key": "ThisIsAStrongAndLongEnoughSecretKeyForTestingPurposes!ChangeInProduction!", "Issuer": "http://localhost:5056", "Audience": "http://localhost:3000"}, "CompanyProfile": {"Name": "Children's Village Inc.", "AddressLine1": "123 Main Street", "AddressLine2": "Suite 100", "City": "Anytown", "State": "ST", "ZipCode": "12345", "Country": "USA", "Phone": "******-123-4567", "Email": "<EMAIL>", "Website": "https://www.childrensvillage.org", "LogoUrl": "/assets/images/logo.png"}, "AzureAd": {"Instance": "https://login.microsoftonline.com/", "Domain": "qualified.domain.name", "TenantId": "********-2222-2222-2222-********2222", "ClientId": "********-1111-1111-****************1", "Scopes": "access_as_user", "CallbackPath": "/signin-oidc"}, "CosmosDb": {"ConnectionString": "AccountEndpoint=https://cshero-cosmosdb.documents.azure.com:443/;AccountKey=****************************************************************************************;", "DatabaseName": "CSHeroReports", "ContainerName": "Reports", "MaxRetryAttempts": 3, "RequestTimeoutSeconds": 30, "MaxConnections": 50}, "BlobStorage": {"ConnectionString": "DefaultEndpointsProtocol=https;EndpointSuffix=core.windows.net;AccountName=csheroblobstorage;AccountKey=****************************************************************************************;BlobEndpoint=https://csheroblobstorage.blob.core.windows.net/;FileEndpoint=https://csheroblobstorage.file.core.windows.net/;QueueEndpoint=https://csheroblobstorage.queue.core.windows.net/;TableEndpoint=https://csheroblobstorage.table.core.windows.net/", "ContainerName": "report-components", "ReportDataContainer": "report-data", "MaxRetryAttempts": 3, "RequestTimeoutSeconds": 30, "MaxConcurrentOperations": 10, "EnableEncryption": true, "DefaultContentType": "application/json"}, "ReportRenderingEngine": {"LLM": {"Provider": "OpenAI", "Model": "gpt-4", "ApiKey": "********************************************************************************************************************************************************************", "MaxTokens": 4000, "TimeoutSeconds": 30}, "Anthropic": {"Provider": "Anthropic", "Model": "claude-3-sonnet-********", "ApiKey": "************************************************************************************************************", "MaxTokens": 4000, "TimeoutSeconds": 30}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}