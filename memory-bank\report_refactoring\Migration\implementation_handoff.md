# SQL Structure Optimization Implementation Handoff

## Project Overview

This handoff document provides a comprehensive plan for implementing SQL structure optimization for multi-storage architecture compatibility. The implementation addresses structural issues identified in the current system and aligns the database design with the target multi-storage strategy.

## Context Summary

### Current State
- **Hybrid Transition**: System partially migrated with redundant storage patterns
- **Data Duplication**: ReportVersion contains both SQL JSON data and storage references
- **Conflicting Columns**: Multiple columns serving similar purposes
- **FK Misalignment**: Traditional relationships don't align with multi-storage patterns

### Target Architecture
- **ReportVersion**: Authoritative version control in SQL with storage references
- **Report Generation**: Dynamic generation based on client/report selection + data filters
- **Template System**: Immutable templates with inheritance (no direct modification)
- **Draft-Based Editing**: Reports exist in draft state until Save operation
- **Flexible Creation**: Template-based OR scratch-built reports

## Implementation Plan Overview

### Phase 1: EF Core Migration Implementation
**Priority**: High  
**Estimated Effort**: 2-3 days  
**Dependencies**: None  

**Deliverables**:
1. Migration script for entity structure changes
2. Updated entity classes with new properties
3. DbContext configuration updates
4. Index optimization for multi-storage queries

### Phase 2: DataSeeder Implementation Updates
**Priority**: High  
**Estimated Effort**: 3-4 days  
**Dependencies**: Phase 1 completion  

**Deliverables**:
1. Enhanced SqlSeeder with new structure support
2. Version-aware CosmosSeeder implementation
3. Complete BlobSeeder for versioned styles and components
4. Updated seed data JSON files
5. Enhanced SeedingCoordinator orchestration

### Phase 3: Blob Storage Structure Design
**Priority**: Medium  
**Estimated Effort**: 2-3 days  
**Dependencies**: Phase 2 completion  

**Deliverables**:
1. Blob storage hierarchy implementation
2. Component and style data models
3. Template inheritance blob structure
4. Multi-tenant blob organization
5. Performance optimization strategies

## Detailed Implementation Steps

### Step 1: Create EF Core Migration

**File**: `FY.WB.CSHero2.Infrastructure/Migrations/{timestamp}_OptimizeMultiStorageStructure.cs`

**Key Changes**:
```csharp
// Add new columns to ReportVersions
- StylesBlobId (nvarchar(255), nullable)
- StylesSize (bigint, default 0)
- StorageStrategy (nvarchar(50), default "SQL")

// Add new columns to Reports
- IsDraft (bit, default true)
- LastSavedAt (datetime2, nullable)
- DraftDataDocumentId (nvarchar(255), nullable)

// Add migration tracking to ReportSections
- IsMigratedToCosmos (bit, default false)
- CosmosDocumentId (nvarchar(255), nullable)
- MigrationDate (datetime2, nullable)
- TemplateSourceSectionId (uniqueidentifier, nullable)
- IsModifiedFromTemplate (bit, default false)

// Add migration tracking to ReportSectionFields
- IsMigratedToCosmos (bit, default false)
- MigrationDate (datetime2, nullable)
- TemplateSourceFieldId (uniqueidentifier, nullable)
- IsModifiedFromTemplate (bit, default false)

// Create ReportStorageMetadata table
- Complete table structure for storage tracking
```

**Validation Steps**:
1. Run migration on development database
2. Verify all new columns have appropriate defaults
3. Test rollback migration functionality
4. Validate index creation and performance

### Step 2: Update Entity Classes

**Files to Modify**:
- `FY.WB.CSHero2.Domain/Entities/ReportVersion.cs`
- `FY.WB.CSHero2.Domain/Entities/Report.cs`
- `FY.WB.CSHero2.Domain/Entities/ReportSection.cs`
- `FY.WB.CSHero2.Domain/Entities/ReportSectionField.cs`

**New Entity**:
- `FY.WB.CSHero2.Domain/Entities/ReportStorageMetadata.cs`

**Key Updates**:
```csharp
// ReportVersion - Add new properties
public string? StylesBlobId { get; set; }
public long StylesSize { get; set; }
public string StorageStrategy { get; set; } = "SQL";

// Report - Add draft support
public bool IsDraft { get; set; } = true;
public DateTime? LastSavedAt { get; set; }
public string? DraftDataDocumentId { get; set; }

// ReportSection - Add migration tracking
public bool IsMigratedToCosmos { get; set; } = false;
public string? CosmosDocumentId { get; set; }
public DateTime? MigrationDate { get; set; }
public Guid? TemplateSourceSectionId { get; set; }
public bool IsModifiedFromTemplate { get; set; } = false;
```

### Step 3: Update DbContext Configuration

**File**: `FY.WB.CSHero2.Infrastructure/Persistence/ApplicationDbContext.cs`

**Key Configurations**:
```csharp
// ReportVersion configuration
entity.Property(e => e.StorageStrategy).HasMaxLength(50).HasDefaultValue("SQL");
entity.Property(e => e.StylesBlobId).HasMaxLength(255);
entity.HasIndex(e => e.StorageStrategy);

// Report configuration
entity.Property(e => e.IsDraft).HasDefaultValue(true);
entity.Property(e => e.DraftDataDocumentId).HasMaxLength(255);
entity.HasIndex(e => e.IsDraft);

// ReportStorageMetadata configuration
entity.HasOne(e => e.Report).WithOne().HasForeignKey<ReportStorageMetadata>(e => e.ReportId);
entity.HasIndex(e => e.ReportId).IsUnique();
```

### Step 4: Update SqlSeeder Implementation

**File**: `FY.WB.CSHero2.Infrastructure/Persistence/Seeders/SqlSeeder.cs`

**Key Enhancements**:
1. **New Seeding Order**:
   ```
   Phase 1: TenantProfiles, Templates
   Phase 2: Clients
   Phase 3: Reports (with new structure)
   Phase 4: ReportVersions (with storage references)
   Phase 5: ReportStorageMetadata
   Phase 6: ReportStyles
   Phase 7: ReportSections, ReportSectionFields (with migration tracking)
   ```

2. **Enhanced Report Seeding**:
   ```csharp
   // Set new properties for multi-storage
   report.IsDraft = false; // Seed data represents saved reports
   report.LastSavedAt = reportDto.CreationTime;
   ```

3. **Version Seeding with Storage Strategy**:
   ```csharp
   version.StorageStrategy = "SQL"; // Start with SQL, upgrade during migration
   version.DataDocumentId = null; // Will be set by CosmosSeeder
   version.ComponentsBlobId = null; // Will be set by BlobSeeder
   version.StylesBlobId = null; // Will be set by BlobSeeder
   ```

### Step 5: Enhance CosmosSeeder Implementation

**File**: `FY.WB.CSHero2.Infrastructure/Persistence/Seeders/CosmosSeeder.cs`

**Key Enhancements**:
1. **Version-Aware Document Creation**:
   ```csharp
   private static string GenerateVersionedDocumentId(Guid reportId, Guid versionId)
   {
       return $"report-data-{reportId}-v{versionId}";
   }
   ```

2. **Enhanced Document Structure**:
   ```csharp
   public class VersionedReportDataDocument
   {
       public Guid VersionId { get; set; }
       public int VersionNumber { get; set; }
       public bool IsDraft { get; set; }
       // ... additional version-specific properties
   }
   ```

3. **Storage Metadata Updates**:
   ```csharp
   // Update storage metadata after Cosmos DB creation
   metadata.CosmosStorageSize = documentSize;
   metadata.StorageStrategy = "Hybrid";
   metadata.MigrationStatus = "InProgress";
   ```

### Step 6: Implement BlobSeeder

**File**: `FY.WB.CSHero2.Infrastructure/Persistence/Seeders/BlobSeeder.cs`

**Key Components**:
1. **Storage Hierarchy Implementation**:
   ```
   /tenants/{tenant-id}/reports/{report-id}/versions/v{version-number}/
   ├── components.json
   ├── styles.json
   └── metadata.json
   ```

2. **Component Blob Creation**:
   ```csharp
   private async Task<ComponentsBlob> CreateComponentsBlobAsync(
       ApplicationDbContext context, 
       ReportVersion version, 
       CancellationToken cancellationToken)
   ```

3. **Style Blob Creation**:
   ```csharp
   private async Task<StylesBlob> CreateStylesBlobAsync(
       ApplicationDbContext context, 
       ReportVersion version, 
       CancellationToken cancellationToken)
   ```

### Step 7: Update Seed Data JSON Files

**Files to Update**:
- `report-sections.json` - Add template inheritance fields
- `report-section-fields.json` - Add template inheritance fields
- `reports.json` - Ensure compatibility with new structure

**New Fields to Add**:
```json
// In report-sections.json
{
  "templateSourceSectionId": null,
  "isModifiedFromTemplate": false
}

// In report-section-fields.json
{
  "templateSourceFieldId": null,
  "isModifiedFromTemplate": false
}
```

## Testing Strategy

### Unit Tests
1. **Migration Tests**:
   - Test migration up/down functionality
   - Verify default values are applied correctly
   - Test index creation

2. **Entity Tests**:
   - Test new property behaviors
   - Validate entity relationships
   - Test audit property updates

3. **Seeder Tests**:
   - Test each seeder independently
   - Verify storage reference linking
   - Test error handling and rollback

### Integration Tests
1. **Multi-Storage Flow Tests**:
   - Test complete seeding workflow
   - Verify data consistency across storage systems
   - Test storage metadata accuracy

2. **Performance Tests**:
   - Measure seeding performance impact
   - Test query performance with new indexes
   - Validate storage size calculations

### Manual Testing
1. **Development Environment**:
   - Run complete migration and seeding
   - Verify all storage systems are populated
   - Test report creation and editing workflows

2. **Data Validation**:
   - Verify storage references are correct
   - Check template inheritance tracking
   - Validate multi-tenant data isolation

## Deployment Checklist

### Pre-Deployment
- [ ] All unit tests passing
- [ ] Integration tests passing
- [ ] Migration tested on development database
- [ ] Seed data validated
- [ ] Performance benchmarks completed

### Deployment Steps
1. **Backup Current Database**
2. **Run Migration**:
   ```bash
   dotnet ef database update --project FY.WB.CSHero2.Infrastructure
   ```
3. **Run Seeding**:
   ```bash
   dotnet run --project FY.WB.CSHero2
   ```
4. **Validate Results**:
   - Check all new tables and columns exist
   - Verify storage references are populated
   - Test report creation workflow

### Post-Deployment
- [ ] Monitor application performance
- [ ] Verify multi-storage functionality
- [ ] Check error logs for issues
- [ ] Validate data consistency

## Risk Mitigation

### Data Consistency Risks
**Risk**: Data inconsistency between storage systems  
**Mitigation**: 
- Implement validation in seeders
- Add data reconciliation processes
- Monitor storage metadata for discrepancies

### Performance Risks
**Risk**: Slower query performance due to new indexes  
**Mitigation**:
- Benchmark before/after performance
- Optimize index strategies
- Monitor query execution plans

### Migration Risks
**Risk**: Migration failure in production  
**Mitigation**:
- Test migration thoroughly in development
- Implement rollback procedures
- Use feature flags for gradual rollout

## Success Criteria

### Functional Requirements
- [ ] All migrations execute successfully
- [ ] Multi-storage seeding completes without errors
- [ ] Storage references are correctly linked
- [ ] Template inheritance tracking works
- [ ] Draft-based editing is supported

### Performance Requirements
- [ ] Seeding completes within acceptable time limits
- [ ] Query performance is maintained or improved
- [ ] Storage size tracking is accurate

### Quality Requirements
- [ ] All tests pass
- [ ] Code coverage maintained
- [ ] Documentation is complete
- [ ] Error handling is robust

## Next Steps After Implementation

### Phase 4: Service Layer Updates (Future)
- Update ReportService to use new storage strategy
- Implement draft management functionality
- Add template inheritance logic

### Phase 5: API Updates (Future)
- Update controllers to support draft workflow
- Add storage strategy endpoints
- Implement template management APIs

### Phase 6: UI Updates (Future)
- Update frontend for draft-based editing
- Add template selection interface
- Implement storage strategy visualization

## Support and Maintenance

### Monitoring
- Track storage metadata accuracy
- Monitor cross-storage query performance
- Alert on seeding failures

### Maintenance Tasks
- Regular storage size optimization
- Template inheritance cleanup
- Migration status monitoring

### Documentation Updates
- Update API documentation
- Create user guides for new features
- Maintain troubleshooting guides

## Contact Information

**Technical Lead**: [To be assigned]  
**Database Administrator**: [To be assigned]  
**DevOps Engineer**: [To be assigned]  

## Appendix

### Related Documents
- [SQL Structure Optimization Plan](./sql_structure_optimization_plan.md)
- [DataSeeder Implementation Plan](./dataseeder_implementation_plan.md)
- [Blob Storage Structure Design](./blob_storage_structure_design.md)
- [Entity Relationships Documentation](../documentation/entity_relationships.md)

### Code References
- Migration files: `FY.WB.CSHero2.Infrastructure/Migrations/`
- Entity definitions: `FY.WB.CSHero2.Domain/Entities/`
- Seeder implementations: `FY.WB.CSHero2.Infrastructure/Persistence/Seeders/`
- Seed data: `FY.WB.CSHero2.Infrastructure/Persistence/SeedData/`

This implementation handoff provides a comprehensive roadmap for optimizing the SQL structure for multi-storage architecture compatibility while supporting the draft-based editing workflow and template inheritance model.
