using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Azure.Cosmos;
using Azure.Storage.Blobs;
using FY.WB.CSHero2.ReportRenderingEngine.Application.Services;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces;
using FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Services;
using FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Configuration;

namespace FY.WB.CSHero2.ReportRenderingEngine.Infrastructure
{
    /// <summary>
    /// Dependency injection configuration for Report Rendering Engine Infrastructure layer
    /// </summary>
    public static class DependencyInjection
    {
        /// <summary>
        /// Adds Report Rendering Engine Infrastructure services to the dependency injection container
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="configuration">Configuration</param>
        /// <returns>Service collection for chaining</returns>
        public static IServiceCollection AddReportRenderingEngineInfrastructure(
            this IServiceCollection services,
            IConfiguration configuration)
        {
            // Configuration Options
            services.Configure<CosmosDbOptions>(configuration.GetSection(CosmosDbOptions.SectionName));
            services.Configure<BlobStorageOptions>(configuration.GetSection(BlobStorageOptions.SectionName));

            // CosmosDB Client
            services.AddSingleton<CosmosClient>(serviceProvider =>
            {
                var connectionString = configuration["CosmosDb:ConnectionString"];
                if (string.IsNullOrEmpty(connectionString))
                {
                    throw new InvalidOperationException("CosmosDb connection string is not configured");
                }

                var cosmosClientOptions = new CosmosClientOptions
                {
                    SerializerOptions = new CosmosSerializationOptions
                    {
                        PropertyNamingPolicy = CosmosPropertyNamingPolicy.CamelCase
                    },
                    ConnectionMode = ConnectionMode.Direct,
                    RequestTimeout = TimeSpan.FromSeconds(30),
                    MaxRetryAttemptsOnRateLimitedRequests = 3,
                    MaxRetryWaitTimeOnRateLimitedRequests = TimeSpan.FromSeconds(30)
                };

                return new CosmosClient(connectionString, cosmosClientOptions);
            });

            // Azure Blob Storage Client
            services.AddSingleton<BlobServiceClient>(serviceProvider =>
            {
                var connectionString = configuration["BlobStorage:ConnectionString"];
                if (string.IsNullOrEmpty(connectionString))
                {
                    throw new InvalidOperationException("BlobStorage connection string is not configured");
                }

                return new BlobServiceClient(connectionString);
            });

            // Storage Services (Phase 2)
            services.AddScoped<IReportStyleService, CosmosDbReportStyleService>();
            services.AddScoped<IReportDataBlobService, AzureBlobReportDataService>();

            // Tenant Resolution Service for seeding
            services.AddScoped<ITenantResolutionService, TenantResolutionService>();

            // Storage Initialization and Health Check
            services.AddScoped<StorageInitializationService>();

            // Seeding Services
            services.AddScoped<CosmosDbSeeder>();
            services.AddScoped<BlobStorageSeeder>();
            services.AddScoped<SeededDataResolutionService>();

            // Export Providers (Phase 5)
            services.AddScoped<IExportProvider, PdfExportProvider>();
            services.AddScoped<IExportProvider, HtmlExportProvider>();

            // Additional export providers can be added here
            // services.AddScoped<IExportProvider, WordExportProvider>();
            // services.AddScoped<IExportProvider, ExcelExportProvider>();
            // services.AddScoped<IExportProvider, PowerPointExportProvider>();

            return services;
        }

        /// <summary>
        /// Ensures that required storage containers and databases are created
        /// </summary>
        /// <param name="serviceProvider">Service provider</param>
        /// <returns>Task</returns>
        public static async Task EnsureStorageCreatedAsync(this IServiceProvider serviceProvider)
        {
            // Ensure CosmosDB database and containers exist
            await EnsureCosmosDbCreatedAsync(serviceProvider);

            // Ensure Blob Storage containers exist
            await EnsureBlobStorageCreatedAsync(serviceProvider);
        }

        private static async Task EnsureCosmosDbCreatedAsync(IServiceProvider serviceProvider)
        {
            var cosmosClient = serviceProvider.GetRequiredService<CosmosClient>();
            var options = serviceProvider.GetRequiredService<Microsoft.Extensions.Options.IOptions<CosmosDbOptions>>().Value;

            // Create database if it doesn't exist
            var databaseResponse = await cosmosClient.CreateDatabaseIfNotExistsAsync(
                options.DatabaseName,
                throughput: 400); // Shared throughput for cost optimization

            var database = databaseResponse.Database;

            // Create containers if they don't exist
            await database.CreateContainerIfNotExistsAsync(
                options.Containers.ReportStyles,
                "/partitionKey",
                throughput: null); // Use shared database throughput
        }

        private static async Task EnsureBlobStorageCreatedAsync(IServiceProvider serviceProvider)
        {
            var blobServiceClient = serviceProvider.GetRequiredService<BlobServiceClient>();
            var options = serviceProvider.GetRequiredService<Microsoft.Extensions.Options.IOptions<BlobStorageOptions>>().Value;

            // Create container if it doesn't exist
            var containerClient = blobServiceClient.GetBlobContainerClient(options.ContainerName);
            await containerClient.CreateIfNotExistsAsync(Azure.Storage.Blobs.Models.PublicAccessType.None);
        }
    }
}
