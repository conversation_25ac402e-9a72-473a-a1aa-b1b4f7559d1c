{"ConnectionStrings": {"DefaultConnection": "Server=.;Database=FY.WB.CSHeroSQL;Trusted_Connection=true;MultipleActiveResultSets=true;", "BlobStorage": "UseDevelopmentStorage=true;"}, "CosmosDb": {"ConnectionString": "AccountEndpoint=https://cshero-cosmosdb.documents.azure.com:443/;AccountKey=****************************************************************************************;", "DatabaseName": "CSHeroReports", "ContainerName": "Reports", "MaxRetryAttempts": 3, "RequestTimeoutSeconds": 30, "MaxConnections": 50}, "BlobStorage": {"ConnectionString": "DefaultEndpointsProtocol=https;EndpointSuffix=core.windows.net;AccountName=csheroblobstorage;AccountKey=****************************************************************************************;BlobEndpoint=https://csheroblobstorage.blob.core.windows.net/;FileEndpoint=https://csheroblobstorage.file.core.windows.net/;QueueEndpoint=https://csheroblobstorage.queue.core.windows.net/;TableEndpoint=https://csheroblobstorage.table.core.windows.net/", "ContainerName": "report-components", "ReportDataContainer": "report-data", "MaxRetryAttempts": 3, "RequestTimeoutSeconds": 30, "MaxConcurrentOperations": 10, "EnableEncryption": true, "DefaultContentType": "application/json"}, "ReportRenderingEngine": {"LLM": {"Provider": "OpenAI", "Model": "gpt-4", "ApiKey": "test-api-key", "MaxTokens": 4000, "TimeoutSeconds": 30}, "ComponentGeneration": {"Framework": "NextJS", "TypeScript": true, "StyleFramework": "TailwindCSS", "ComponentLibrary": "Custom"}, "Export": {"PdfEngine": "<PERSON><PERSON><PERSON><PERSON><PERSON>har<PERSON>", "OfficeEngine": "OpenXML", "TempDirectory": "./temp/exports"}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "FY.WB.CSHero2": "Debug"}}}