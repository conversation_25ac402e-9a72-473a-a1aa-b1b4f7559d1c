using FY.WB.CSHero2.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace FY.WB.CSHero2.Infrastructure.Persistence.Configurations
{
    public class ReportConfiguration : IEntityTypeConfiguration<Report>
    {
        public void Configure(EntityTypeBuilder<Report> builder)
        {
            builder.HasKey(r => r.Id);

            builder.Property(r => r.ReportNumber)
                .IsRequired()
                .HasMaxLength(20); // e.g., "CSR-2025-001"

            builder.HasIndex(r => r.ReportNumber)
                .IsUnique()
                .HasDatabaseName("IX_Reports_ReportNumber");

            // V2 Indexes
            builder.HasIndex(r => r.TemplateId)
                .HasDatabaseName("IX_Reports_TemplateId");

            builder.HasIndex(r => r.CurrentVersionId)
                .HasDatabaseName("IX_Reports_CurrentVersionId");

            builder.HasIndex(r => r.ReportType)
                .HasDatabaseName("IX_Reports_ReportType");

            builder.HasIndex(r => new { r.TemplateId, r.ReportType })
                .HasDatabaseName("IX_Reports_TemplateId_ReportType");

            builder.Property(r => r.ClientId)
                .IsRequired();

            builder.Property(r => r.ClientName)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(r => r.Name)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(r => r.Category)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(r => r.SlideCount)
                .IsRequired();

            builder.Property(r => r.Status)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(r => r.Author)
                .IsRequired()
                .HasMaxLength(100);

            // V2 Properties
            builder.Property(r => r.TemplateId)
                .IsRequired(false); // Nullable for non-template-based reports

            builder.Property(r => r.CurrentVersionId)
                .IsRequired(false); // Nullable until first version is created

            builder.Property(r => r.ReportType)
                .IsRequired()
                .HasMaxLength(50)
                .HasDefaultValue("Standard");

            // V3 Multi-storage Properties
            builder.Property(r => r.DataDocumentId)
                .IsRequired(false)
                .HasMaxLength(100);

            builder.Property(r => r.ComponentsBlobId)
                .IsRequired(false)
                .HasMaxLength(255);

            // Configure relationship with Client
            builder.HasOne(r => r.Client)
                .WithMany() // No navigation property on Client side yet
                .HasForeignKey(r => r.ClientId)
                .OnDelete(DeleteBehavior.Restrict); // Don't cascade delete reports when client is deleted

            // V2 Relationships
            builder.HasOne(r => r.Template)
                .WithMany(t => t.Reports)
                .HasForeignKey(r => r.TemplateId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.HasMany(r => r.Versions)
                .WithOne(rv => rv.Report)
                .HasForeignKey(rv => rv.ReportId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasMany(r => r.Sections)
                .WithOne(s => s.Report)
                .HasForeignKey(s => s.ReportId)
                .OnDelete(DeleteBehavior.Cascade);

            // Audit properties are handled by base entity configuration
            builder.Property(r => r.CreationTime)
                .IsRequired();

            builder.Property(r => r.LastModificationTime)
                .IsRequired(false);
        }
    }
}
