using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using FY.WB.CSHero2.Domain.Entities;

namespace FY.WB.CSHero2.Infrastructure.Persistence.Configurations
{
    /// <summary>
    /// Entity Framework configuration for ReportStorageMetadata entity
    /// </summary>
    public class ReportStorageMetadataConfiguration : IEntityTypeConfiguration<ReportStorageMetadata>
    {
        public void Configure(EntityTypeBuilder<ReportStorageMetadata> builder)
        {
            // Table configuration
            builder.ToTable("ReportStorageMetadata");

            // Primary key
            builder.HasKey(rsm => rsm.Id);

            // Properties
            builder.Property(rsm => rsm.ReportId)
                .IsRequired();

            builder.Property(rsm => rsm.StorageStrategy)
                .IsRequired()
                .HasMaxLength(50)
                .HasDefaultValue("SQL");

            builder.Property(rsm => rsm.MigrationStatus)
                .IsRequired()
                .HasMaxLength(50)
                .HasDefaultValue("NotMigrated");

            builder.Property(rsm => rsm.MigrationStartDate)
                .IsRequired(false);

            builder.Property(rsm => rsm.MigrationCompletedDate)
                .IsRequired(false);

            builder.Property(rsm => rsm.MigrationErrorMessage)
                .IsRequired(false)
                .HasMaxLength(1000);

            builder.Property(rsm => rsm.SqlStorageSize)
                .IsRequired()
                .HasDefaultValue(0);

            builder.Property(rsm => rsm.CosmosStorageSize)
                .IsRequired()
                .HasDefaultValue(0);

            builder.Property(rsm => rsm.BlobStorageSize)
                .IsRequired()
                .HasDefaultValue(0);

            builder.Property(rsm => rsm.TotalStorageSize)
                .IsRequired()
                .HasDefaultValue(0);

            builder.Property(rsm => rsm.AccessCount)
                .IsRequired()
                .HasDefaultValue(0);

            builder.Property(rsm => rsm.LastAccessDate)
                .IsRequired(false);

            builder.Property(rsm => rsm.PerformanceMetrics)
                .IsRequired(false)
                .HasColumnType("nvarchar(max)");

            builder.Property(rsm => rsm.OptimizationMetadata)
                .IsRequired(false)
                .HasColumnType("nvarchar(max)");

            // Indexes
            builder.HasIndex(rsm => rsm.ReportId)
                .IsUnique()
                .HasDatabaseName("IX_ReportStorageMetadata_ReportId");

            builder.HasIndex(rsm => rsm.StorageStrategy)
                .HasDatabaseName("IX_ReportStorageMetadata_StorageStrategy");

            builder.HasIndex(rsm => rsm.MigrationStatus)
                .HasDatabaseName("IX_ReportStorageMetadata_MigrationStatus");

            builder.HasIndex(rsm => rsm.TotalStorageSize)
                .HasDatabaseName("IX_ReportStorageMetadata_TotalStorageSize");

            builder.HasIndex(rsm => rsm.LastAccessDate)
                .HasDatabaseName("IX_ReportStorageMetadata_LastAccessDate");

            // Relationships
            builder.HasOne(rsm => rsm.Report)
                .WithOne() // No navigation property on Report side yet
                .HasForeignKey<ReportStorageMetadata>(rsm => rsm.ReportId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
