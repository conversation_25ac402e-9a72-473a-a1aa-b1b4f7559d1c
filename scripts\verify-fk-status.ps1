# Simple verification of foreign key relationships
$ErrorActionPreference = "Stop"

# File paths
$reportsPath = "FY.WB.CSHero2.Infrastructure/Persistence/SeedData/reports.json"
$reportVersionsPath = "FY.WB.CSHero2.Infrastructure/Persistence/SeedData/report-versions.json"

Write-Host "=== Foreign Key Status Verification ===" -ForegroundColor Cyan

# Load data
$reports = Get-Content $reportsPath -Raw | ConvertFrom-Json
$reportVersions = Get-Content $reportVersionsPath -Raw | ConvertFrom-Json

Write-Host "Reports: $($reports.Count)" -ForegroundColor Green
Write-Host "ReportVersions: $($reportVersions.Count)" -ForegroundColor Green

# Create set of valid Report IDs
$validReportIds = @{}
foreach ($report in $reports) {
    $validReportIds[$report.id] = $true
}

# Check each ReportVersion
$mismatches = @()
for ($i = 0; $i -lt $reportVersions.Count; $i++) {
    $version = $reportVersions[$i]
    $reportId = $version.reportId
    
    if (-not $validReportIds.ContainsKey($reportId)) {
        $mismatches += @{
            Index = $i
            VersionId = $version.id
            InvalidReportId = $reportId
        }
    }
}

if ($mismatches.Count -eq 0) {
    Write-Host "`n✅ All foreign key relationships are VALID!" -ForegroundColor Green
    Write-Host "All $($reportVersions.Count) ReportVersions reference existing Reports." -ForegroundColor Green
} else {
    Write-Host "`n❌ Found $($mismatches.Count) foreign key mismatches:" -ForegroundColor Red
    foreach ($mismatch in $mismatches) {
        Write-Host "  ReportVersion [$($mismatch.Index)] ($($mismatch.VersionId)) -> Invalid Report ID: $($mismatch.InvalidReportId)" -ForegroundColor Red
    }
}

# Show first few mappings for verification
Write-Host "`nFirst 10 ReportVersion -> Report mappings:" -ForegroundColor Yellow
for ($i = 0; $i -lt [Math]::Min(10, $reportVersions.Count); $i++) {
    $version = $reportVersions[$i]
    $reportId = $version.reportId
    $exists = $validReportIds.ContainsKey($reportId)
    $status = if ($exists) { "OK" } else { "FAIL" }
    Write-Host "  [$i] $($version.id) -> $reportId $status" -ForegroundColor $(if ($exists) { "Green" } else { "Red" })
}
