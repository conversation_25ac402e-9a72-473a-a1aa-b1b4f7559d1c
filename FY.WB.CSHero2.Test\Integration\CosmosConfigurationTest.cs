using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using FY.WB.CSHero2.Infrastructure.Configuration;
using Xunit;

namespace FY.WB.CSHero2.Test.Integration
{
    public class CosmosConfigurationTest : IntegrationTestBase
    {
        [Fact]
        public void Should_Load_CosmosDb_Configuration_Correctly()
        {
            // Arrange & Act
            var cosmosOptions = ServiceProvider.GetRequiredService<IOptions<CosmosDbOptions>>().Value;
            var configuration = ServiceProvider.GetRequiredService<IConfiguration>();

            // Debug output - let's see what's actually being loaded
            var connectionStringFromConfig = configuration["CosmosDb:ConnectionString"];
            var databaseNameFromConfig = configuration["CosmosDb:DatabaseName"];
            var containerNameFromConfig = configuration["CosmosDb:ContainerName"];

            // Output debug information
            System.Console.WriteLine($"Configuration Debug:");
            System.Console.WriteLine($"  CosmosDb:ConnectionString = '{connectionStringFromConfig}'");
            System.Console.WriteLine($"  CosmosDb:DatabaseName = '{databaseNameFromConfig}'");
            System.Console.WriteLine($"  CosmosDb:ContainerName = '{containerNameFromConfig}'");
            System.Console.WriteLine($"CosmosDbOptions Debug:");
            System.Console.WriteLine($"  ConnectionString = '{cosmosOptions.ConnectionString}'");
            System.Console.WriteLine($"  DatabaseName = '{cosmosOptions.DatabaseName}'");
            System.Console.WriteLine($"  ContainerName = '{cosmosOptions.ContainerName}'");

            // For now, just check that we can get the configuration
            Assert.NotNull(cosmosOptions);
            
            // If the connection string is empty, let's see what we can find
            if (string.IsNullOrEmpty(cosmosOptions.ConnectionString))
            {
                // Check if the configuration files exist
                var testProjectPath = Path.Combine(Directory.GetCurrentDirectory(), "FY.WB.CSHero2.Test");
                var mainProjectPath = Path.Combine(Directory.GetCurrentDirectory(), "FY.WB.CSHero2");
                var testConfigPath = Path.Combine(testProjectPath, "appsettings.Test.json");
                var mainConfigPath = Path.Combine(mainProjectPath, "appsettings.json");
                
                System.Console.WriteLine($"File existence check:");
                System.Console.WriteLine($"  Test config exists: {File.Exists(testConfigPath)} at {testConfigPath}");
                System.Console.WriteLine($"  Main config exists: {File.Exists(mainConfigPath)} at {mainConfigPath}");
                
                Assert.True(false, $"ConnectionString is empty. Check debug output above.");
            }
            
            // Verify it's not the emulator connection string
            Assert.DoesNotContain("localhost:8081", cosmosOptions.ConnectionString);
            Assert.Contains("cshero-cosmosdb.documents.azure.com", cosmosOptions.ConnectionString);
        }
    }
}
